# NotebookImageSaver 使用指南

## 概述

NotebookImageSaver 是一个 Kubernetes 自定义资源，用于将运行中的 Notebook 容器保存为镜像并推送到容器镜像仓库。

## 功能特性

- 自动检测 Notebook 运行状态
- 支持自定义容器名称
- 支持私有镜像仓库认证
- 完整的状态跟踪和条件管理
- 详细的事件记录和日志

## 字段说明

### Spec 字段

| 字段名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `notebookName` | string | 是 | - | 要保存镜像的 Notebook 名称 |
| `notebookNamespace` | string | 否 | 当前命名空间 | Notebook 所在的命名空间 |
| `containerName` | string | 否 | "notebook" | 要保存的容器名称 |
| `repository` | string | 是 | - | 目标镜像仓库地址 |
| `tag` | string | 是 | - | 镜像标签 |
| `registrySecret` | string | 否 | - | 镜像仓库认证密钥名称 |
| `imageSaverImage` | string | 否 | cetccloud/notebook-image-saver:latest | 镜像保存器使用的镜像 |

### Status 字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `phase` | string | 当前阶段：Pending/Running/Succeeded/Failed |
| `conditions` | []Condition | 详细的条件状态 |
| `jobName` | string | 创建的 Job 名称 |
| `nodeName` | string | Notebook 运行的节点名称 |
| `containerID` | string | 容器 ID |
| `imageDigest` | string | 保存的镜像摘要 |
| `message` | string | 状态描述信息 |
| `startTime` | Time | 开始时间 |
| `completionTime` | Time | 完成时间 |

## 使用步骤

### 1. 准备镜像仓库认证（可选）

如果使用私有镜像仓库，需要创建认证密钥：

```bash
kubectl create secret docker-registry my-registry-secret \
  --docker-server=my-registry.com \
  --docker-username=myuser \
  --docker-password=mypassword \
  --docker-email=<EMAIL>
```

### 2. 创建 NotebookImageSaver

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: my-notebook-saver
  namespace: default
spec:
  notebookName: "my-jupyter-notebook"
  repository: "my-registry.com/notebooks/my-notebook"
  tag: "v1.0.0"
  registrySecret: "my-registry-secret"
```

### 3. 应用配置

```bash
kubectl apply -f notebook-image-saver.yaml
```

### 4. 监控状态

```bash
# 查看状态
kubectl get notebookimagesaver my-notebook-saver -o yaml

# 查看事件
kubectl describe notebookimagesaver my-notebook-saver

# 查看相关 Job
kubectl get jobs -l notebook-image-saver.name=my-notebook-saver
```

## 状态和阶段

### 阶段说明

- **Pending**: 初始化阶段，验证 Notebook 状态和获取容器信息
- **Running**: 镜像保存作业正在执行
- **Succeeded**: 镜像保存成功
- **Failed**: 镜像保存失败

### 条件类型

- **Ready**: 整体就绪状态
- **JobCreated**: 作业创建状态
- **ImageSaved**: 镜像保存状态

## 常见使用场景

### 1. 定期备份

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: daily-backup
spec:
  notebookName: "research-notebook"
  repository: "backup-registry.com/notebooks/research"
  tag: "backup-$(date +%Y%m%d)"
```

### 2. 实验快照

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: experiment-snapshot
spec:
  notebookName: "ml-experiment"
  repository: "experiments.com/ml/experiment"
  tag: "checkpoint-v1.2"
```

### 3. 生产部署

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: production-release
spec:
  notebookName: "model-serving-notebook"
  repository: "prod-registry.com/models/serving"
  tag: "release-2025.01"
  registrySecret: "prod-registry-secret"
```

## 故障排除

### 常见错误

1. **Notebook 未找到**
   - 检查 `notebookName` 和 `notebookNamespace` 是否正确
   - 确认 Notebook 存在且处于运行状态

2. **容器未找到**
   - 检查 `containerName` 是否正确
   - 查看 Notebook Pod 中的容器列表

3. **镜像推送失败**
   - 检查镜像仓库地址是否正确
   - 验证认证密钥是否有效
   - 确认网络连接正常

4. **权限错误**
   - 确认 ServiceAccount 有足够权限
   - 检查 RBAC 配置

### 调试命令

```bash
# 查看详细状态
kubectl get notebookimagesaver <name> -o yaml

# 查看事件
kubectl describe notebookimagesaver <name>

# 查看作业日志
kubectl logs job/<job-name>

# 查看作业状态
kubectl describe job <job-name>
```

## 最佳实践

1. **标签命名**: 使用有意义的标签，包含版本或时间戳
2. **命名空间**: 合理组织不同环境的资源
3. **认证管理**: 安全地管理镜像仓库认证信息
4. **监控**: 设置适当的监控和告警
5. **清理**: 定期清理不需要的镜像和资源

## 安全考虑

1. **权限最小化**: 只授予必要的权限
2. **密钥管理**: 使用 Kubernetes Secrets 管理敏感信息
3. **网络安全**: 确保镜像仓库访问的网络安全
4. **审计**: 启用审计日志记录操作
