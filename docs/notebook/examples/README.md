# Notebook 使用指南

## 一、简介

本文档介绍如何使用 cetccloud-operator 部署和管理 Jupyter Notebook 实例。通过简单的 YAML 配置，您可以快速创建和管理 Notebook 环境。

## 二、快速开始

### 2.1 最小配置示例

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  name: demo                    # Notebook 实例名称
  labels:
    app: demo                  # 标签，用于分类和筛选
spec:
  template:
    # 这里的字段和pod 一样,pod 有的字段,这里都支持
    spec:
      containers:
      - name: notebook         # 容器名称
        image: kubeflownotebookswg/jupyter-scipy:v1.8.0  # 容器镜像
        resources:            # 资源配置
          limits:             # 资源上限
            cpu: "0.6"
            memory: 1.2Gi
          requests:           # 资源请求
            cpu: "0.5"
            memory: 1Gi
```

### 2.2 定时停止功能

您可以通过添加注解来设置 Notebook去停止

```yaml
metadata:
  annotations:
    cetccloud.ai/stop-nb: "2025-03-24T08:31:47Z"   ##  cetccloud.ai/stop-nb有前缀就行,value 随便,建议取值为当前时间
```

## 三、操作指南

### 3.1 部署 Notebook

```bash
# 创建 Notebook
kubectl apply -f jupyter.yaml

# 查看 Notebook 状态
kubectl get notebook

# 查看对应的 Pod
kubectl get pod -l app=demo

# 删除 Notebook
kubectl delete -f jupyter.yaml
```

### 3.2 查看日志和状态

```bash
# 查看 Pod 日志
kubectl logs <pod-名称>

# 查看详细状态
kubectl describe notebook <notebook-名称>

# 查看事件
kubectl get events --sort-by='.lastTimestamp'
```

## 四、配置说明

### 4.1 必填参数

| 参数 | 说明 | 示例值 |
|------|------|--------|
| metadata.name | 实例名称 | demo |
| spec.template.spec.containers[0].name | 容器名称 | notebook |
| spec.template.spec.containers[0].image | 容器镜像 | kubeflownotebookswg/jupyter-scipy:v1.8.0 |

### 4.2 资源配置建议

| 资源类型 | 最小建议值 | 推荐值 |
|----------|------------|--------|
| CPU 请求  | 0.5       | 1.0    |
| CPU 限制  | 0.6       | 2.0    |
| 内存请求  | 1Gi       | 2Gi    |
| 内存限制  | 1.2Gi     | 4Gi    |

### 4.3 状态说明

Notebook 实例在生命周期中会经历不同的状态，您可以通过 `kubectl get notebook` 命令查看当前状态。 字段为 NotebookState

| 状态 | 说明 | 处理建议 |
|------|------|----------|
| Pending | Notebook 正在初始化中，资源创建后的准备阶段 | 等待系统初始化完成 |
| Queuing | Notebook 已提交并进入队列等待启动 | 等待系统调度分配资源 |
| Running | Notebook 正常运行中，所有资源已成功启动 | 可以正常访问和使用 |
| Stopping | Notebook 正在停止过程中，进行资源清理 | 等待停止流程完成 |
| Stopped | Notebook 已完全停止，所有资源已清理完成 | 可以安全删除或重新启动 |
| Failed | Notebook 启动失败，资源创建过程中遇到错误 | 查看事件和日志排查原因 |

状态流转说明：
1. 创建后进入 `Pending` 状态
2. 资源准备就绪后进入 `Queuing` 状态等待调度
3. 调度成功并启动后进入 `Running` 状态
4. 停止时先进入 `Stopping` 状态
5. 清理完成后进入 `Stopped` 状态
6. 如果启动过程中出错则进入 `Failed` 状态

### 4.4 访问方式

访问地址格式：`nodeip:serviceNodePort/notebook/notebook-name`


