apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  annotations:
    cetccloud.ai/stop-nb: "2025-03-24T08:31:47Z"  # 定时停止时间
  name: demo
  namespace: default
spec:
  notebookTemplate:
    podTemplate:
      spec:
        containers:
        - name: notebook
          image: kubeflownotebookswg/jupyter-scipy:v1.8.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: "0.5"
              memory: 1Gi
            requests:
              cpu: "0.5"
              memory: 1Gi



