# 自动创建 pvc
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  annotations:
    # 默认自动创建 pvc, size 为 50Gi
    cetccloud.ai/storage-size: 100Gi
    # 默认自动删除 pvc, 设置为 false 时, 不会自动删除
    cetccloud.ai/storage-auto-delete: "false"
  labels:
    app: demo
    cetccloud.ai/notebook-name: demo
  name: demo
  namespace: default
spec:
  podTemplate:
    spec:
      containers:
        - name: notebook
          image: kubeflownotebookswg/jupyter-scipy:v1.8.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: "0.5"
              memory: 1Gi
            requests:
              cpu: "0.5"
              memory: 1Gi
---
# 传入已创建好的 pvc
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  labels:
    app: demo
    cetccloud.ai/notebook-name: demo
  name: demo
  namespace: default
spec:
  podTemplate:
    spec:
      containers:
        - name: notebook
          image: kubeflownotebookswg/jupyter-scipy:v1.8.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: "0.5"
              memory: 1Gi
            requests:
              cpu: "0.5"
              memory: 1Gi
      volumeMounts:
      - mountPath: /data
        name: data
    volumes:
    - name: data
      persistentVolumeClaim:
        claimName: workspace