# NotebookImageSaver 示例集合

---
# 基础示例 - 保存 notebook 镜像到公共仓库
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: basic-image-saver
  namespace: default
spec:
  notebookName: "my-jupyter-notebook"
  repository: "docker.io/myuser/my-notebook"
  tag: "v1.0.0"

---
# 完整示例 - 包含所有可选字段
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: full-featured-image-saver
  namespace: default
  annotations:
    description: "Save notebook with custom settings"
spec:
  notebookName: "data-science-notebook"
  notebookNamespace: "data-science"
  containerName: "notebook"
  repository: "my-private-registry.com/data-science/notebook"
  tag: "experiment-2025-01-03"
  registrySecret: "private-registry-secret"
  imageSaverImage: "cetccloud/notebook-image-saver:v1.2.0"

---
# 跨命名空间示例 - 保存其他命名空间的 notebook
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: cross-namespace-saver
  namespace: ops
spec:
  notebookName: "ml-training-notebook"
  notebookNamespace: "ml-team"
  repository: "harbor.company.com/ml-team/training-notebook"
  tag: "checkpoint-$(date +%Y%m%d-%H%M%S)"
  registrySecret: "harbor-registry-secret"

---
# 自定义容器示例 - 保存特定容器
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: custom-container-saver
  namespace: research
spec:
  notebookName: "multi-container-notebook"
  containerName: "tensorflow-container"  # 保存特定的容器
  repository: "gcr.io/my-project/tensorflow-notebook"
  tag: "research-v2.1"
  registrySecret: "gcr-secret"

---
# 时间戳标签示例 - 使用动态标签
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: timestamped-saver
  namespace: default
  annotations:
    # 注意：实际使用时，标签应该在创建时确定，这里只是示例
    timestamp: "2025-01-03T10:30:00Z"
spec:
  notebookName: "experiment-notebook"
  repository: "my-registry.com/experiments/notebook"
  tag: "exp-20250103-103000"  # 格式：exp-YYYYMMDD-HHMMSS

---
# 最小配置示例 - 只包含必需字段
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: minimal-saver
  namespace: default
spec:
  notebookName: "simple-notebook"
  repository: "localhost:5000/simple-notebook"
  tag: "latest"

---
# 企业级示例 - 包含标签和注解
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: enterprise-saver
  namespace: production
  labels:
    team: "data-science"
    project: "customer-analytics"
    environment: "production"
    version: "v1.0"
  annotations:
    description: "Production notebook image for customer analytics"
    owner: "<EMAIL>"
    created-by: "automated-pipeline"
    backup-policy: "retain-30-days"
spec:
  notebookName: "customer-analytics-notebook"
  repository: "enterprise-registry.company.com/data-science/customer-analytics"
  tag: "prod-v1.0.0"
  registrySecret: "enterprise-registry-secret"
  imageSaverImage: "enterprise-registry.company.com/tools/notebook-image-saver:stable"
