# 自动创建 pvc
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  annotations:
    # 默认自动创建 pvc, size 为 50Gi
    cetccloud.ai/storage-size: 100Gi
    # 默认自动删除 pvc, 设置为 false 时, 不会自动删除
    cetccloud.ai/storage-auto-delete: "false"
  labels:
    app: demo
    cetccloud.ai/notebook-name: demo
  name: demo
  namespace: default
spec:
  # 模型 URI
  modelURI: cc://root/image-classifier
  # 数据集 URI
  dataSetURI: cc://root/image-classifier
  podTemplate:
    spec:
      # 使用已有的 serviceAccount，该 serviceAccount 中 secret 需要有访问模型和数据集的权限
      serviceAccountName: dbp-10-200-8-248-30080
      containers:
        - name: notebook
          image: kubeflownotebookswg/jupyter-scipy:v1.8.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: "0.5"
              memory: 1Gi
            requests:
              cpu: "0.5"
              memory: 1Gi