apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  labels:
    app: demo
    cetccloud.ai/notebook-name: demo
  name: demo
  namespace: default
spec:
  podTemplate:
      spec:
        containers:
        - name: notebook
          image: kubeflownotebookswg/jupyter-scipy:v1.8.0
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: "0.5"
              memory: 1Gi
            requests:
              cpu: "0.5"
              memory: 1Gi
