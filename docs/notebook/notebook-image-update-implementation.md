# Notebook 镜像更新实现说明

## 概述

本文档详细说明了 Notebook 镜像更新功能的实现原理，确保 StatefulSet 只能被 Notebook Controller 更新，避免直接修改 StatefulSet 导致的同步问题。

## 核心设计原则

### 1. 单一控制源
- **只有 Notebook Controller 可以更新 StatefulSet**
- 所有对 StatefulSet 的修改都必须通过 Notebook CRD 的变更触发
- 使用 `controllerutil.CreateOrUpdate` 确保一致性

### 2. 标准化流程
- 镜像更新复用现有的 `CreateOrUpdateNotebook` 流程
- 通过 Backend 系统统一处理资源更新
- 保持与其他资源更新的一致性

### 3. 状态管理
- 引入 `NotebookStateUpdating` 状态
- 使用注解标记更新过程
- 完整的事件记录和错误处理

## 实现架构

```
Notebook CRD 变更
       ↓
Notebook Controller 检测镜像变更
       ↓
设置状态为 Updating + 添加注解
       ↓
调用 CreateOrUpdateNotebook
       ↓
Backend 系统处理
       ↓
StatefulSet 更新 (通过 controllerutil.CreateOrUpdate)
       ↓
监控更新进度
       ↓
完成后清理注解 + 恢复 Running 状态
```

## 关键组件

### 1. 镜像变更检测

```go
func (n *NotebookReconciler) shouldUpdateImage(ctx context.Context, notebook *systemv1alpha1.Notebook) bool {
    // 比较 Notebook spec 中的镜像与 StatefulSet 中的当前镜像
    // 只在 Running 状态下检测
}
```

### 2. 镜像更新处理

```go
func (n *NotebookReconciler) processImageUpdate(ctx context.Context, notebook *systemv1alpha1.Notebook) (ctrl.Result, error) {
    // 1. 设置状态为 Updating
    // 2. 添加更新注解
    // 3. 调用 CreateOrUpdateNotebook (标准流程)
    // 4. 记录事件
}
```

### 3. 更新状态监控

```go
func (n *NotebookReconciler) processUpdatingState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
    // 1. 检查 StatefulSet 更新状态
    // 2. 监控 Pod 就绪状态
    // 3. 处理成功/失败情况
    // 4. 清理注解
}
```

## 状态流转

```
Running → (检测到镜像变更) → Updating → (更新完成) → Running
   ↑                                           ↓
   ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
   
Running → (检测到镜像变更) → Updating → (更新失败) → Failed
```

## 注解管理

### IMAGE_UPDATE_ANNOTATION
- **用途**: 标记镜像更新正在进行
- **值**: 更新开始时间 (RFC3339 格式)
- **生命周期**: 更新开始时添加，完成或失败时删除

```go
const IMAGE_UPDATE_ANNOTATION = "cetccloud.ai/image-updating"
```

## Backend 系统集成

### StatefulSet Backend
- 使用 `controllerutil.CreateOrUpdate` 确保原子性更新
- 通过 `MutateFn` 应用所有变更
- 自动处理 OwnerReference

```go
func (s *Statefulset) Configure(notebook *systemv1alpha1.Notebook) error {
    // 使用 CreateOrUpdate 确保一致性
    return controllerutil.CreateOrUpdate(context.TODO(), s.client, statefulSet, s.MutateFn(statefulSet, notebook))
}
```

### BuildStatefulSet
- 从 Notebook spec 构建完整的 StatefulSet 规格
- 包括镜像、资源、环境变量等所有配置
- 确保 StatefulSet 与 Notebook 规格完全同步

## 错误处理

### 1. 镜像拉取失败
- 检测 Pod 状态
- 超时机制 (5分钟)
- 自动标记为失败

### 2. 资源不足
- 监控 Pod 调度状态
- 记录详细错误信息
- 保留现有 Pod 运行

### 3. 配置错误
- 验证镜像格式
- 检查容器配置
- 提供明确的错误消息

## 监控和可观测性

### 事件记录
- `ImageUpdating`: 开始更新
- `ImageUpdated`: 更新启动成功
- `UpdateCompleted`: 更新完成
- `UpdateFailed`: 更新失败

### 日志记录
- 详细的更新进度日志
- 错误信息和堆栈跟踪
- 性能指标 (更新耗时)

### 状态查询
```bash
# 查看更新状态
kubectl get notebook <name> -o jsonpath='{.status.notebookState}'

# 查看更新注解
kubectl get notebook <name> -o jsonpath='{.metadata.annotations.cetccloud\.ai/image-updating}'

# 查看事件
kubectl describe notebook <name>
```

## 安全考虑

### 1. 权限控制
- 只有有权限修改 Notebook CRD 的用户才能触发镜像更新
- StatefulSet 不能被直接修改

### 2. 镜像安全
- 验证镜像来源
- 支持私有镜像仓库认证
- 镜像扫描集成点

### 3. 资源隔离
- 更新过程中保持资源限制
- 防止资源泄露
- 网络策略保持不变

## 性能优化

### 1. 增量更新
- 只更新变更的字段
- 避免不必要的 Pod 重启
- 保持数据持久性

### 2. 并发控制
- 同一时间只允许一个更新操作
- 队列化多个更新请求
- 避免资源竞争

### 3. 缓存优化
- 缓存镜像信息
- 减少 API 调用
- 提高响应速度

## 测试策略

### 1. 单元测试
- 镜像变更检测逻辑
- 状态转换逻辑
- 错误处理路径

### 2. 集成测试
- 端到端镜像更新流程
- 失败场景测试
- 并发更新测试

### 3. 性能测试
- 大规模镜像更新
- 资源使用监控
- 更新时间测量

## 故障排除

### 常见问题
1. **更新卡住**: 检查 Pod 状态和资源
2. **镜像拉取失败**: 验证镜像地址和认证
3. **权限错误**: 检查 RBAC 配置
4. **资源不足**: 检查集群资源

### 调试工具
- kubectl describe
- 事件查看
- 日志分析
- 状态检查脚本
