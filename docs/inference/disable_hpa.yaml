apiVersion: v1
kind: ServiceAccount
metadata:
  name: ccserviceacc
secrets:
  - name: storage-config
---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    # serving.kserve.io/autoscalerClass: external
    # 此注解指定使用外部自动扩缩机制而非Kubernetes内置的HPA或KPA
    # 当设置为"external"时：
    # 1. KServe会保留Deployment中的replicas字段，而不是将其设为nil
    # 2. 系统会尊重minReplicas设置并用它来设置初始副本数
    # 3. 不会创建HPA资源，而是期望由外部控制器管理Pod的扩缩
    # 4. 适用于需要自定义扩缩逻辑或与第三方自动扩缩器集成的场景
    serving.kserve.io/autoscalerClass: external 
  name: cetccloud-deepseek
spec:
  predictor:
    # minReplicas: 2
    # 指定InferenceService部署的最小副本数
    # 作用：
    # 1. 确保始终至少有指定数量的Pod副本在运行，即使在低负载或无负载情况下
    # 2. 当与autoscalerClass: external一起使用时，此值会被直接用于设置Deployment的replicas字段
    # 3. 提供服务可用性保证，防止所有Pod都被缩减导致服务中断
    # 4. 对于需要GPU等特殊资源的模型服务尤为重要，可以确保资源始终可用
    # 5. 可以根据预期负载、资源可用性和服务级别要求来设置
    minReplicas: 2
    serviceAccountName: ccserviceacc
    runtimeClassName: nvidia
    model:
      protocolVersion: v2
      modelFormat:
        name: huggingface
      args:
        - --model_name=deepseek
        - --model_dir=/mnt/models/
        - --dtype=half
        - --backend=vllm
        - --tensor-parallel-size=1
        - --gpu_memory_utilization=0.85
        - --max_length=8092
      storageUri: cc://root/DeepSeek-R1-Distill-Qwen-1.5B
      resources:
        limits:
          cpu: "6"
          memory: 16Gi
          nvidia.com/gpu: "1"
        requests:
          cpu: "6"
          memory: 16Gi
          nvidia.com/gpu: "1"
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
    volumes :
    - name: dshm
      emptyDir:
        medium: Memory
        sizeLimit: 2Gi