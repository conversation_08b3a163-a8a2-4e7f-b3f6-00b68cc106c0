apiVersion: v1
data:
  CETCCLOUD_ENDPOINT: ************************************  ## 模型库地址
  CETCCLOUD_HUB_TOKEN: ************************************ ## 模型库token ,公开仓库为 ""
kind: Secret
metadata:
  name: storage-config
  namespace: default
type: Opaque
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ccserviceacc
secrets:
  - name: storage-config
---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: cetccloud-custom-deploy-with-hostpath    # 推理服务的名称，用户可自定义
  annotations:
    "serving.kserve.io/enable-nodeport": "true"  
    "nginx.ingress.kubernetes.io/enable-cors": "true"  
spec:
  predictor:
    runtimeClassName: nvidia       # 当底层是k3s且使用GPU时，需要指定runtimeClassName为nvidia
    containers:
    - image: *************:3443/inference/amd64/qwen/qwen-omni:2.5-cu121-dev-xrwang-0408    # 推理服务使用的镜像 ,用户自定义
      name: kserve-container       # 容器名称
      command: ["/bin/bash", "-c"] ## 这里的/bin/bash是容器启动命令，用户需要根据实际情况填写, 也许镜像是/bin/sh
      args:
        - python -m vllm.entrypoints.openai.api_server 
          --model /mnt/models  ## 这里的/mnt/models是挂载的模型数据卷，用户需要根据实际情况挂载模型数据
          --served-model-name "qwen-omni-7b"  ## 这里的qwen-omni-7b是模型名称，用户需要根据实际情况填写
          --host 0.0.0.0  ## 这里的0.0.0.0是服务地址，用户需要根据实际情况填写
          --port 8000  ## 这里的8000是服务端口号，用户需要根据实际情况填写
          --dtype bfloat16  ## 这里的bfloat16是模型数据类型，用户需要根据实际情况填写
          --tensor-parallel-size 2  ## 这里的2是tensor并行大小，用户需要根据实际情况填写
          --gpu-memory-utilization 0.95  ## 这里的0.95是GPU内存利用率，用户需要根据实际情况填写
      ports:
        - containerPort: 8000    # 服务端口号，确保与您的应用监听端口一致
      resources:                 # 资源限制配置
        limits:                  # 资源上限配置
          cpu: "4"              # CPU核心数
          memory: 8Gi           # 内存大小
          nvidia.com/gpu: "2"   # GPU数量
        requests:               # 资源请求配置
          cpu: "4"             # 最小CPU需求
          memory: 8Gi          # 最小内存需求
          nvidia.com/gpu: "2"  # 最小GPU需求
      volumeMounts:            # 数据卷挂载配置
      - mountPath: /data/code  # 容器内挂载路径
        name: mount-code       # 挂载的数据卷名称
      - mountPath: /mnt/models
        name: model-volume
      - mountPath: /dev/shm  ## 支持用户挂载共享内存,可选,当用户选择多张卡时，建议挂载共享内存
        name: dshm
    ## 支持用户挂载代码,可选    
    initContainers:        
    - args:                   
      - sh
      - -c
      - set -e;git clone http://172.28.1.130/wangxingrui/Qwen2.5-Omni-Server.git -b master;ls -l    # 克隆代码仓库
      image: *************:3443/inference/amd64/git:2.34.5       # Git操作镜像
      imagePullPolicy: IfNotPresent                              # 镜像拉取策略
      name: code-init                                            # 初始化容器名称
      resources: {}                                              # 初始化容器资源配置
      workingDir: /data/code                                          # 初始化容器工作目录 ,可选 默认为/workspace
      volumeMounts:                                              # 初始化容器数据卷挂载
      - mountPath: /data/code                                         # 初始化容器挂载路径
        name: mount-code                                         # 挂载的数据卷名称
    volumes:                                                     # 数据卷定义
    - emptyDir: {}                                              # 使用临时存储卷
      name: mount-code                                          # 数据卷名称
    # 新增模型数据卷
    - name: model-volume
      hostPath:
        path: /home/<USER>/Qwen/Qwen2.5-Omni-7B
        type: Directory
    - name: dshm ## 支持用户挂载共享内存,可选,当用户选择多张卡时，建议挂载共享内存
      emptyDir:
        medium: Memory
        sizeLimit: 64Gi





# curl -X POST ************:267/v1/chat/completions \
# -H "Authorization: Bearer $DASHSCOPE_API_KEY" \
# -H "Content-Type: application/json" \
# -d '{
#     "model": "qwen-omni-7b",
#     "messages": [
#    {
#       "role": "system",
#       "content": [{"type":"text","text": "You are a helpful assistant."}]},
#     {
#       "role": "user",
#       "content": [
#         {
#           "type": "image_url",
#           "image_url": {
#             "url": "https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241022/emyrja/dog_and_girl.jpeg"
#           }
#         },
#         {
#           "type": "text",
#           "text": "图中描绘的是什么景象？"
#         }
#       ]
#     }
#   ],
#     "stream":true,
#     "stream_options":{
#         "include_usage":true
#     },
#     "modalities":["text"]
# }'
