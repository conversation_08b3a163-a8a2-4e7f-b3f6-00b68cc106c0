
### onnx 模型部署 使用 triton server  进行部署
```bash
## Triton 要求模型存储库（Model Repository）遵循严格的目录结构：
model_repository/
├── model_A/               # 模型A目录
│   ├── 1/                 # 版本1（必须是数字）
│   │   └── model_files    # 模型权重/配置等
│   ├── config.pbtxt       # 模型配置（必须）
│   └── (optional files)   # 其他文件
├── model_B/               # 模型B目录
│   ├── ...                # 类似结构
└── ...

# 关键文件说明
# config.pbtxt

# 必须包含模型名称、平台类型（如 tensorrt_plan、onnxruntime_onnx、python 等）、输入/输出张量定义。
# 示例：
# Protobuf
# name: "my_model"
# platform: "tensorrt_plan"
# input { name: "input0", dims: [3, 224, 224], data_type: TYPE_FP32 }
# output { name: "output0", dims: [1000], data_type: TYPE_FP32 }
# 版本目录（如 1/）

# 子目录名必须为整数（表示版本号），目录内包含模型文件（如 .plan、.onnx 或 Python 脚本等）。
# 3. 支持的平台
# TensorRT、ONNX、PyTorch、TensorFlow SavedModel 等。
```
```yaml
apiVersion: "serving.kserve.io/v1beta1"
kind: "InferenceService"
metadata:
  name: "style-sample"
  annotations:
    "serving.kserve.io/enable-nodeport": "true"  
spec:
  predictor:
    model:
      protocolVersion: v2  ##必须指定onnx的协议版本为v2。否则，将收到未找到运行时的错误。
      modelFormat:
        name: onnx
      storageUri: "cc://root/onnx" 
      args:
        - --http-port=8090 ### 指定http的 访问的端口 ,默认为8080
      resources:
        limits:
          cpu: "2"
          memory: 4Gi
          nvidia.com/gpu: "1"
        requests:
          cpu: "2"
          memory: 4Gi
          nvidia.com/gpu: "1"
    serviceAccountName: dbp-10-200-8-248-30080
```

### 查看模型状态、模型名称
``` bash
curl --location --request POST 'http://************:30080/v2/repository/index' \
--header 'Host: style-sample-default.example.com'

```

# - --model-store=/mnt/models
# - --grpc-port=9000
# - --http-port=8080
# - --allow-grpc=true
# - --allow-http=true



### onnx模型 请求案例 参考 http://***********/ai/Model-Examples