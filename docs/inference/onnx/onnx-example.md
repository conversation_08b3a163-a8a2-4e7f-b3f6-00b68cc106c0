
# ONNX 模型部署使用 Triton Server 进行部署

## 1. Triton Server 模型存储库结构详解

Triton 要求模型存储库（Model Repository）遵循严格的目录结构：

```bash
model_repository/
├── model_A/               # 模型A目录
│   ├── 1/                 # 版本1（必须是数字）
│   │   └── model.onnx     # ONNX模型文件
│   ├── config.pbtxt       # 模型配置（必须）
│   └── (optional files)   # 其他文件
├── model_B/               # 模型B目录
│   ├── 2/                 # 版本2
│   │   └── model.onnx
│   ├── config.pbtxt
│   └── ...
└── resnet50/              # 示例：ResNet50模型
    ├── 1/
    │   └── model.onnx
    └── config.pbtxt
```

### 关键文件说明

#### config.pbtxt 配置文件详解

必须包含模型名称、平台类型（如 tensorrt_plan、onnxruntime_onnx、python 等）、输入/输出张量定义。

**基础配置示例：**
```protobuf
name: "my_model"
platform: "onnxruntime_onnx"        # ONNX模型固定使用此平台
max_batch_size: 8                   # 最大批处理大小

input [
  {
    name: "input0"                  # 输入张量名称
    data_type: TYPE_FP32             # 数据类型
    dims: [ 3, 224, 224 ]           # 输入维度 [通道, 高度, 宽度]
  }
]

output [
  {
    name: "output0"                 # 输出张量名称
    data_type: TYPE_FP32             # 数据类型
    dims: [ 1000 ]                  # 输出维度
  }
]
```

**完整配置示例（包含优化选项）：**
```protobuf
name: "resnet50"
platform: "onnxruntime_onnx"
max_batch_size: 8

input [
  {
    name: "input"
    data_type: TYPE_FP32
    dims: [ 3, 224, 224 ]
  }
]

output [
  {
    name: "output"
    data_type: TYPE_FP32
    dims: [ 1000 ]
  }
]

# 实例组配置
instance_group [
  {
    count: 1                        # 实例数量
    kind: KIND_GPU                  # 使用GPU
  }
]

# 动态批处理配置
dynamic_batching {
  max_queue_delay_microseconds: 100
}

# 优化配置
optimization {
  execution_accelerators {
    gpu_execution_accelerator : [ {
      name : "tensorrt"
      parameters { key: "precision_mode" value: "FP16" }
    } ]
  }
}
```

#### 版本目录（如 1/）

子目录名必须为整数（表示版本号），目录内包含模型文件（如 .plan、.onnx 或 Python 脚本等）。

#### 支持的平台
- **ONNX**: `onnxruntime_onnx`
- **TensorRT**: `tensorrt_plan`
- **PyTorch**: `pytorch_libtorch`
- **TensorFlow**: `tensorflow_savedmodel`
- **Python**: `python`

## 2. 完整部署示例

### 步骤1：准备模型文件

```bash
# 创建模型存储库目录结构
mkdir -p model_repository/resnet50/1

# 将ONNX模型文件复制到版本目录
cp your_model.onnx model_repository/resnet50/1/model.onnx

# 创建配置文件
cat > model_repository/resnet50/config.pbtxt << EOF
name: "resnet50"
platform: "onnxruntime_onnx"
max_batch_size: 8

input [
  {
    name: "input"
    data_type: TYPE_FP32
    dims: [ 3, 224, 224 ]
  }
]

output [
  {
    name: "output"
    data_type: TYPE_FP32
    dims: [ 1000 ]
  }
]
EOF
```

### 步骤2：Kubernetes 部署配置

```yaml
apiVersion: "serving.kserve.io/v1beta1"
kind: "InferenceService"
metadata:
  name: "onnx-resnet50"
  annotations:
    "serving.kserve.io/enable-nodeport": "true"  
spec:
  predictor:
    model:
      protocolVersion: v2              # 必须指定ONNX的协议版本为v2
      modelFormat:
        name: onnx
      storageUri: "cc://root/onnx"      # 模型存储路径
      args:
        - --http-port=8090             # 指定HTTP访问端口，默认为8080
        - --model-store=/mnt/models    # 模型存储目录
        - --grpc-port=9000             # GRPC端口
        - --allow-grpc=true
        - --allow-http=true
      resources:
        limits:
          cpu: "2"
          memory: 4Gi
          nvidia.com/gpu: "1"           # GPU资源
        requests:
          cpu: "2"
          memory: 4Gi
          nvidia.com/gpu: "1"
    serviceAccountName: dbp-10-200-8-248-30080
```

## 3. 模型验证和测试

### 查看模型状态、模型名称

```bash
# 查看所有已加载的模型
curl --location --request POST 'http://************:30080/v2/repository/index' \
--header 'Host: onnx-resnet50-default.example.com'

# 查看特定模型信息
curl 'http://************:30080/v2/models/resnet50' \
--header 'Host: onnx-resnet50-default.example.com'

# 检查模型是否准备就绪
curl 'http://************:30080/v2/models/resnet50/ready' \
--header 'Host: onnx-resnet50-default.example.com'
```

### 发送推理请求示例

```bash
# 发送推理请求
curl -X POST 'http://************:30080/v2/models/resnet50/infer' \
--header 'Host: onnx-resnet50-default.example.com' \
--header 'Content-Type: application/json' \
--data '{
  "inputs": [
    {
      "name": "input",
      "shape": [1, 3, 224, 224],
      "datatype": "FP32",
      "data": [/* 你的图像数据数组，长度为1*3*224*224=150528 */]
    }
  ]
}'
```

### Python 客户端示例

```python
import numpy as np
import requests
import json

# 准备输入数据
input_data = np.random.rand(1, 3, 224, 224).astype(np.float32)

# 构建请求
payload = {
    "inputs": [
        {
            "name": "input",
            "shape": [1, 3, 224, 224],
            "datatype": "FP32",
            "data": input_data.flatten().tolist()
        }
    ]
}

# 发送请求
response = requests.post(
    'http://************:30080/v2/models/resnet50/infer',
    headers={
        'Host': 'onnx-resnet50-default.example.com',
        'Content-Type': 'application/json'
    },
    data=json.dumps(payload)
)

# 解析结果
result = response.json()
print("推理结果:", result['outputs'][0]['data'])
```

## 4. 常见配置和优化

### 数据类型对应关系
- `TYPE_FP32` → 32位浮点数
- `TYPE_FP16` → 16位浮点数
- `TYPE_INT32` → 32位整数
- `TYPE_INT64` → 64位整数
- `TYPE_UINT8` → 8位无符号整数
- `TYPE_STRING` → 字符串类型

### 性能优化配置

```protobuf
# 启用模型预热
model_warmup [
  {
    name: "sample_request"
    batch_size: 1
    inputs {
      key: "input"
      value: {
        data_type: TYPE_FP32
        dims: [ 3, 224, 224 ]
        zero_data: true
      }
    }
  }
]

# 设置最大队列延迟
dynamic_batching {
  preferred_batch_size: [ 4, 8 ]
  max_queue_delay_microseconds: 500
}
```

## 5. 故障排除和调试

### 常见问题

1. **模型加载失败**
   - 检查 config.pbtxt 语法
   - 验证输入输出名称与ONNX模型一致
   - 确认文件路径正确

2. **推理请求失败**
   - 检查输入数据格式和维度
   - 验证数据类型匹配
   - 确认网络连接

3. **性能问题**
   - 调整批处理大小
   - 启用GPU加速
   - 优化模型配置

### 调试命令

```bash
# 查看服务器日志
kubectl logs -f deployment/onnx-resnet50-predictor

# 检查服务状态
kubectl get inferenceservice onnx-resnet50

# 查看详细事件
kubectl describe inferenceservice onnx-resnet50
```

## 6. 参考资源

- ONNX模型请求案例参考: http://172.28.0.44/ai/Model-Examples
- Triton Server 官方文档: https://github.com/triton-inference-server/server
- KServe 官方文档: https://kserve.github.io/website/

## 7. 高级配置示例

### 多输入多输出模型

```protobuf
name: "multi_io_model"
platform: "onnxruntime_onnx"
max_batch_size: 4

input [
  {
    name: "image_input"
    data_type: TYPE_FP32
    dims: [ 3, 224, 224 ]
  },
  {
    name: "text_input"
    data_type: TYPE_INT64
    dims: [ 512 ]
  }
]

output [
  {
    name: "classification_output"
    data_type: TYPE_FP32
    dims: [ 1000 ]
  },
  {
    name: "feature_output"
    data_type: TYPE_FP32
    dims: [ 2048 ]
  }
]
```

### 自定义预处理和后处理

```protobuf
name: "custom_pipeline"
platform: "python"
max_batch_size: 8

input [
  {
    name: "raw_image"
    data_type: TYPE_UINT8
    dims: [ -1 ]  # 可变长度
  }
]

output [
  {
    name: "predictions"
    data_type: TYPE_FP32
    dims: [ 1000 ]
  }
]
```

