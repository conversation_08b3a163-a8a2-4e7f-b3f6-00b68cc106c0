apiVersion: v1  
kind: Secret  
metadata:  
  name: cchub-localmodel-config 
  namespace: cchub-localmodel-jobs 
type: Opaque 
data: 
  CETCCLOUD_ENDPOINT: aHR0cDovLzEwLjIwMC44LjI0ODozMDA4MA==  # 模型库地址 http://10.200.8.248:30080
  CETCCLOUD_HUB_TOKEN: ZG1kbWRtZW1lbW0= 

---  
apiVersion: serving.kserve.io/v1alpha1  
kind: LocalModelNodeGroup  # 自定义资源，用于定义模型存储节点组
metadata:  
  name: default  
spec:  
  # 表示整个节点组可以使用的最大存储容量上限
  # 这是一个逻辑限制，用于控制所有模型缓存的总大小
  # 值为 5TB，表示所有缓存的模型总大小不能超过 5TB
  storageLimit: 1T 
  persistentVolumeClaimSpec:  # 持久卷声明(PVC)规格，用于请求存储
    accessModes:  # 访问模式
      - ReadWriteOnce  # 可以被单个节点以读写方式挂载
    resources:  # 资源需求
      requests:  # 资源请求
        storage: 1000G  # 请求的存储大小(50吉字节)
    storageClassName: local-path  # 使用的存储类(local-path表示本地存储)
    volumeMode: Filesystem  # 卷模式为文件系统
    volumeName: models  # 卷的名称
  persistentVolumeSpec:  # 持久卷(PV)规格，用于提供存储
    accessModes:  # 访问模式
      - ReadWriteOnce  # 可以被单个节点以读写方式挂载
    volumeMode: Filesystem  # 卷模式为文件系统
    capacity:  # 卷的容量
      storage: 1000G  # 存储容量(100吉字节)
    storageClassName: local-path  # 使用的存储类
    local:  # 本地卷配置
      path: /models  # 卷在节点上的路径
    nodeAffinity:  # 节点亲和性规则
      required:  # 必需的节点选择器条件
        nodeSelectorTerms:  # 节点选择器条件列表
        - matchExpressions:  # 节点选择器要求列表
          - key: nvidia.com/gpu.product  # 要匹配的节点标签键
            operator: In  # 匹配操作符
            values:  # 要匹配的值
            - "NVIDIA-GeForce-RTX-4090-D"  # 只使用带有NVIDIA-GeForce-RTX-4090-D GPU的节点


---  
apiVersion: "serving.kserve.io/v1alpha1"  
kind: ClusterStorageContainer  
metadata:  
  name: cc-hub-clusterstoragecontainer
spec:  
  container:  
    name: storage-initializer  #
    image: hub.cetccloud.io:5000/jdcloud/kserve/storage-initializer:v0.15.0  
    env:  
    - name: CETCCLOUD_HUB_TOKEN  
      valueFrom:  
        secretKeyRef: 
          name: cchub-localmodel-config   
          key: CETCCLOUD_HUB_TOKEN  
          optional: false  
    - name: CETCCLOUD_ENDPOINT 
      valueFrom:  
        secretKeyRef:  
          name: cchub-localmodel-config   #
          key: CETCCLOUD_ENDPOINT  
          optional: false  
    resources: 
      requests:  
        memory: 100Mi  
        cpu: 100m  
      limits:  
        memory: 1Gi  
        cpu: "1"  
  supportedUriFormats:  # 支持的URI格式
    - prefix: cc://  # 模型URI前缀，用于从CETC Cloud模型库获取模型
  workloadType: localModelDownloadJob  # 工作负载类型，用于下载模型





## 需要缓存的模型
---
apiVersion: serving.kserve.io/v1alpha1
kind: LocalModelCache
metadata:
  name: qwen3-8b
spec:
  sourceModelUri: cc://root/Qwen3-8B 
  modelSize: 30Gi
  nodeGroups: 
  - default




### 节点缓存了哪些模型
# [root@aicloud249 xrwang]# kubectl  get localmodelnode aicloud249 -o yaml
# apiVersion: serving.kserve.io/v1alpha1
# kind: LocalModelNode
# metadata:
#   creationTimestamp: "2025-05-13T02:45:30Z"
#   generation: 5
#   name: aicloud249
#   resourceVersion: "15619648"
#   uid: a3ab1c32-a378-43e0-a068-d81b1adb3255
# spec:
#   localModels:
#   - modelName: qwen3-8b
#     sourceModelUri: cc://root/Qwen3-8B
# status:
#   modelStatus:
#     qwen3-8b: ModelDownloaded

# ModelNotDownloaded - 模型尚未下载
# ModelDownloading - 模型正在下载中
# ModelDownloaded - 模型已成功下载
# ModelDownloadFailed - 模型下载失败
# ModelDeleting - 模型正在被删除
# ModelDeleteFailed - 模型删除失败


### 模型缓存状态
# [root@aicloud249 xrwang]# kubectl  get localmodelcaches.serving.kserve.io qwen3-8b  -o yaml
# apiVersion: serving.kserve.io/v1alpha1
# kind: LocalModelCache
# metadata:
#   annotations:
#     kubectl.kubernetes.io/last-applied-configuration: |
#       {"apiVersion":"serving.kserve.io/v1alpha1","kind":"LocalModelCache","metadata":{"annotations":{},"name":"qwen3-8b"},"spec":{"modelSize":"30Gi","nodeGroups":["default"],"sourceModelUri":"cc://root/Qwen3-8B"}}
#   creationTimestamp: "2025-05-13T02:52:54Z"
#   finalizers:
#   - localmodel.kserve.io/finalizer
#   generation: 1
#   name: qwen3-8b
#   resourceVersion: "15635446"
#   uid: 66ce3f50-0d40-42de-a057-899ef4d15321
# spec:
#   modelSize: 30Gi
#   nodeGroups:
#   - default
#   sourceModelUri: cc://root/Qwen3-8B
# status:
#   copies:
#     available: 1
#     total: 1
#   nodeStatus:
#     aicloud249: NodeDownloaded

# NodeNotDownloaded - 节点尚未下载模型
# NodeDownloading - 节点正在下载模型
# NodeDownloaded - 节点已成功下载模型
# NodeDownloadFailed - 节点下载模型失败
# NodeDeleting - 节点正在删除模型
# NodeDeleteFailed - 节点删除模型失败
# NodeUnknown - 节点状态未知



### 使用方式
