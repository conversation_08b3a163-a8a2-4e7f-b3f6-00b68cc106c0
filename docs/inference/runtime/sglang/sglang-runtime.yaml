
apiVersion: serving.kserve.io/v1alpha1
kind: ClusterServingRuntime
metadata:
  name: sglang-runtime
spec:
  supportedModelFormats:
    - name: sglang
      priority: 1
      version: "1"
      autoSelect: true
  containers:
    - name: kserve-container
      image: hub.cetccloud.io:5000/jdcloud/inference/amd64/lmsysorg/sglang:v0.4.5-cu124
      command:
        - python3
        - -m
        - sglang.launch_server
      args:
        - --model-path=/mnt/models
      resources:
        limits:
          cpu: "1"
          memory: 2Gi
        requests:
          cpu: "1"
          memory: 2Gi
