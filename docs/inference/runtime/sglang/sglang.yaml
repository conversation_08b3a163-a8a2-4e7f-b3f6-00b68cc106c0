apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: glang
spec:
  predictor:
    volumes:
    - name: dshm
      emptyDir:
        medium: Memory
        sizeLimit: 32Gi  # 根据模型大小调整共享内存大小
    model:
      modelFormat:
        name: sglang
      storageUri: cc://root/Qwen3-8B
      resources:
        limits:
          cpu: "32"
          memory: 32768M
          nvidia.com/gpu: "2"
        requests:
          cpu: "32"
          memory: 32768M
          nvidia.com/gpu: "2"
      image: hub.cetccloud.io:5000/jdcloud/inference/amd64/lmsysorg/sglang:v0.4.5-cu124
      args:
        - --model-path=/mnt/models
      # 添加容器共享内存挂载
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
    nodeSelector:
      nvidia.com/gpu.product: NVIDIA-GeForce-RTX-4090-D
    runtimeClassName: nvidia


