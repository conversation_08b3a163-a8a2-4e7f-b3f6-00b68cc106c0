apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: custom-sglang
spec:
  predictor:
    volumes:
    - name: dshm
      emptyDir:
        medium: Memory
        sizeLimit: 32Gi  # 根据模型大小调整共享内存大小
    model:
      modelFormat:
        name: custom
      storageUri: cc://root/Qwen3-8B
      resources:
        limits:
          cpu: "32"
          memory: 32768M
          nvidia.com/gpu: "2"
        requests:
          cpu: "32"
          memory: 32768M
          nvidia.com/gpu: "2"
      image: hub.cetccloud.io:5000/jdcloud/inference/amd64/lmsysorg/sglang:v0.4.5-cu124
      command:
        - python3
        - -m
        - sglang.launch_server
      args:
        - --model-path=/mnt/models
        - --served-model-name "qwen-3-8b"
        - --host 0.0.0.0
        - --port 8000
        - --dtype bfloat16
        - --tp-size 2
        - --mem-fraction-static 0.8
      # 添加容器端口配置
      ports:
        - containerPort: 8000
          protocol: TCP
          name: http
      # 添加容器共享内存挂载
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
    nodeSelector:
      nvidia.com/gpu.product: NVIDIA-GeForce-RTX-4090-D
    runtimeClassName: nvidia