
apiVersion: serving.kserve.io/v1alpha1
kind: ClusterServingRuntime
metadata:
  name: custom-runtime
spec:
  supportedModelFormats:
    - name: custom
      priority: 1
      version: "1"
      autoSelect: true
  containers:
    - name: kserve-container
      image: hub.cetccloud.io:5000/jdcloud/inference/amd64/lmsysorg/sglang:v0.4.5-cu124 ## 用户自定义镜像 ,这里只是占位用
      resources:
        limits:
          cpu: "1"
          memory: 2Gi
        requests:
          cpu: "1"
          memory: 2Gi
