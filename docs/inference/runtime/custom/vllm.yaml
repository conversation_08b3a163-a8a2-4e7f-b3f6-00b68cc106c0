apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: custom-vllm
spec:
  predictor:
    volumes:
    - name: dshm
      emptyDir:
        medium: Memory
        sizeLimit: 32Gi  # 根据模型大小调整共享内存大小
    model:
      modelFormat:
        name: custom
      storageUri: cc://root/Qwen3-8B
      resources:
        limits:
          cpu: "32"
          memory: 32768M
          nvidia.com/gpu: "2"
        requests:
          cpu: "32"
          memory: 32768M
          nvidia.com/gpu: "2"
      image: hub.cetccloud.io:5000/jdcloud/inference/amd64/vllm:v8.15 ## 用户自定义镜像 ,这里是必须要传,不然会使用runtime 默认的
      command:
        - python3
        - -m
        - vllm.entrypoints.openai.api_server.launch_server
      args:
        - --model=/mnt/models
        - --served-model-name "qwen-3-8b"
        - --host 0.0.0.0
        - --port 8000
        - --dtype bfloat16
        - --tensor-parallel-size 2
        - --gpu-memory-utilization 0.95
      # 添加容器共享内存挂载
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
    nodeSelector:
      nvidia.com/gpu.product: NVIDIA-GeForce-RTX-4090-D
    runtimeClassName: nvidia


