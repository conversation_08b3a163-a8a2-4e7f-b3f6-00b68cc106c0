apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: cetccloud-deepseek
  annotations:
    serving.kserve.io/enable-nodeport: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"

spec:
  predictor:
    nodeSelector:
      nvidia.com/gpu.product: Tesla-T4
    serviceAccountName: ccserviceacc
    runtimeClassName: nvidia
    volumes:
    - name: dshm
      emptyDir:
        medium: Memory
        sizeLimit: 8Gi  # 根据模型大小调整共享内存大小
    model:
      protocolVersion: v1
      modelFormat:
        name: huggingface
      args:
        - --model_name=deepseek
        - --model_dir=/mnt/models/DeepSeek-R1-Distill-Qwen-1.5B/
        - --backend=vllm
        - --tensor-parallel-size=1
        - --dtype=half
        - --gpu_memory_utilization=0.95
        - --max_length=8092
        - --enable-chunked-prefill=False
        # 添加共享内存相关参数
        - --shm-size=8Gi
      resources:
        limits:
          cpu: "6"
          memory: 24Gi
          nvidia.com/gpu: "1"
          ephemeral-storage: 64Gi
          # 添加共享内存资源限制
          hugepages-2Mi: 8Gi  # 使用大页内存提高性能
        requests:
          cpu: "6"
          memory: 24Gi
          nvidia.com/gpu: "1"
          hugepages-2Mi: 8Gi
      # 添加容器共享内存挂载
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
