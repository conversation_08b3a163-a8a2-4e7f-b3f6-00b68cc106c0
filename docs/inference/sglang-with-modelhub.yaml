apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: cetccloud-custom-sglang-deploy    # 推理服务的名称，用户可自定义
  annotations:
    "serving.kserve.io/enable-nodeport": "true"
    "nginx.ingress.kubernetes.io/enable-cors": "true"
spec:
  predictor:
    runtimeClassName: nvidia       # 当底层是k3s且使用GPU时，需要指定runtimeClassName为nvidia
    containers:
    - args:
      - --model-path /mnt/models
      - --served-model-name "deepseek-r1-1.5b"
      - --host 0.0.0.0
      - --port 8000
      - --dtype bfloat16
      - --tp-size 1
      - --mem-fraction-static 0.8
      name: kserve-container       # 容器名称
      command:
      - python3
      - -m
      - sglang.launch_server
      workingDir: /code      # 容器的默认工作目录 ,默认为/code,支持用户传入
      image: hub.cetccloud.io:5000/jdcloud/inference/amd64/lmsysorg/sglang:v0.4.5-cu124
      ports:
        - containerPort: 8000    # 服务端口号，确保与您的应用监听端口一致
      resources:                 # 资源限制配置
        limits:                  # 资源上限配置
          cpu: "24"              # CPU核心数
          memory: 48Gi           # 内存大小
          nvidia.com/gpu: "1"   # GPU数量
        requests:               # 资源请求配置
          cpu: "24"             # 最小CPU需求
          memory: 48Gi          # 最小内存需求
          nvidia.com/gpu: "1"  # 最小GPU需求
      volumeMounts:            # 数据卷挂载配置
      - mountPath: /code  # 容器内挂载路径
        name: mount-code       # 挂载的数据卷名称
      - mountPath: /dev/shm  ## 支持用户挂载共享内存,可选,当用户选择多张卡时，建议挂载共享内存
        name: dshm
      - mountPath: /mnt/models
        name: model-volume
    ## 支持用户挂载代码,可选
    initContainers:
    - args:
      - cc://root/DeepSeek-R1-Distill-Qwen-1.5B
      - /mnt/models
      env:
      - name: CETCCLOUD_ENDPOINT
        valueFrom:
          secretKeyRef:
            key: CETCCLOUD_ENDPOINT
            name: dbp-10-200-9-249-30080
      - name: CETCCLOUD_HUB_TOKEN
        valueFrom:
          secretKeyRef:
            key: CETCCLOUD_HUB_TOKEN
            name: dbp-10-200-9-249-30080
      image: hub.cetccloud.io:5000/jdcloud/inferece/storage-initializer:v0.14.1
      name: storage-initializer
    volumes:                                                     # 数据卷定义
    - emptyDir: {}                                              # 使用临时存储卷
      name: mount-code                                          # 数据卷名称
    # 新增模型数据卷
    - name: model-volume
      emptyDir: {}
    - name: dshm ## 支持用户挂载共享内存,可选,当用户选择多张卡时，建议挂载共享内存
      emptyDir:
        medium: Memory
        sizeLimit: 64Gi





# curl -X POST 10.200.9.249:267/v1/chat/completions \
# -H "Authorization: Bearer $DASHSCOPE_API_KEY" \
# -H "Content-Type: application/json" \
# -d '{
#     "model": "qwen-omni-7b",
#     "messages": [
#    {
#       "role": "system",
#       "content": [{"type":"text","text": "You are a helpful assistant."}]},
#     {
#       "role": "user",
#       "content": [
#         {
#           "type": "image_url",
#           "image_url": {
#             "url": "https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241022/emyrja/dog_and_girl.jpeg"
#           }
#         },
#         {
#           "type": "text",
#           "text": "图中描绘的是什么景象？"
#         }
#       ]
#     }
#   ],
#     "stream":true,
#     "stream_options":{
#         "include_usage":true
#     },
#     "modalities":["text"]
# }'

