
# TorchServe 对接文档

## 模型结构要求

### 模型存储格式
# TorchServe 需要使用 Model Archive File (MAR) 格式打包模型
# MAR 文件包含模型权重、模型定义和处理逻辑

### 模型目录结构
# 标准的 TorchServe 模型目录结构如下:
# ├── config
# │   ├── config.properties
# ├── model-store
# │   ├── mnist.mar


### 模型处理程序
# 需要实现以下处理程序:
# - initialize: 加载模型
# - preprocess: 处理输入数据
# - inference: 执行推理
# - postprocess: 处理输出数据

## 部署方式

kind: "InferenceService"
apiVersion: "serving.kserve.io/v1beta1"
metadata:
  name: "torchserve-model"
spec:
  predictor:
    model:
      modelFormat:
        name: pytorch  # 指定使用 PyTorch 格式
      protocolVersion: v2  # 使用 V2 协议 ## 这里可表示协议,默认为v2
      runtime: kserve-torchserve  # 当该字段有 zhi的时候 ,会强制使用 runtime运行时,没有值的时候,会自动匹配支持pytorch模型格式的运行时
      storageUri: "cc://root/image-classifier"  # 模型存储位置
      # 可选: 资源配置
      # resources:
      #   limits:
      #     memory: 4Gi
      #     nvidia.com/gpu: "1"  # GPU 配置

## 请求方式 以下 路由都是标准的torchserver 路由,没有进行任何封装



### REST API (V1 协议)
## 查询模型名称
# http://************:30080/v1/models
## 返回格式
# {
#     "models": [
#         "mnist"
#     ]
# }

# 预测请求格式:
# POST /v1/models/{model_name}:predict
#
# 请求体示例 (图像分类):
# {
#   "data": "base64编码的图像数据"
# }
#
# 响应格式:
# {
#   "predictions": [预测结果]
# }

## 请求示例
# curl --location 'http://************:30080/v1/models/mnist:predict' \
# --header 'Host: torchserve-default.example.com' \
# --header 'Content-Type: application/json' \
# --data '{
#   "instances": [
#     {
#       "data": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAAw0lEQVR4nGNgGFggVVj4/y8Q2GOR83n+58/fP0DwcSqmpNN7oOTJw6f+/H2pjUU2JCSEk0EWqN0cl828e/FIxvz9/9cCh1zS5z9/G9mwyzl/+PNnKQ45nyNAr9ThMHQ/UG4tDofuB4bQIhz6fIBenMWJQ+7Vn7+zeLCbKXv6z59NOPQVgsIcW4QA9YFi6wNQLrKwsBebW/68DJ388Nun5XFocrqvIFH59+XhBAxThTfeB0r+vP/QHbuDCgr2JmOXoSsAAKK7bU3vISS4AAAAAElFTkSuQmCC",
#       "target": 0
#     }
#   ]
# }'
## 返回
# {
#     "predictions": [
#         [
#             2
#         ]
#     ]
# }


## 模型可解释
# curl --location 'http://************:30080/v1/models/mnist:explain' \
# --header 'Host: torchserve-default.example.com' \
# --header 'Content-Type: application/json' \
# --data '{
#   "instances": [
#     {
#       "data": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAAw0lEQVR4nGNgGFggVVj4/y8Q2GOR83n+58/fP0DwcSqmpNN7oOTJw6f+/H2pjUU2JCSEk0EWqN0cl828e/FIxvz9/9cCh1zS5z9/G9mwyzl/+PNnKQ45nyNAr9ThMHQ/UG4tDofuB4bQIhz6fIBenMWJQ+7Vn7+zeLCbKXv6z59NOPQVgsIcW4QA9YFi6wNQLrKwsBebW/68DJ388Nun5XFocrqvIFH59+XhBAxThTfeB0r+vP/QHbuDCgr2JmOXoSsAAKK7bU3vISS4AAAAAElFTkSuQmCC",
#       "target": 0
#     }
#   ]
# }'







### REST API (V2 开放推理协议)


## 查询模型名称
# http://************:30080/v2/models
## 返回格式
# {
#     "models": [
#         "mnist"
#     ]
# }


# 预测请求格式:
# POST /v2/models/{model_name}/infer
#
# 请求体示例 (张量输入):
# {
#   "inputs": [
#     {
#       "name": "input-0",
#       "shape": [1, 3, 224, 224],
#       "datatype": "FP32",
#       "data": [...]
#     }
#   ]
# }
#
# 响应格式:
# {
#   "id": "请求ID",
#   "model_name": "模型名称",
#   "model_version": "模型版本",
#   "outputs": [
#     {
#       "name": "输出名称",
#       "shape": [输出形状],
#       "datatype": "数据类型",
#       "data": [输出数据]
#     }
#   ]
# }


# curl --location 'http://************:30080/v2/models/mnist/infer' \
# --header 'Host: torchserve-default.example.com' \
# --header 'Content-Type: application/json' \
# --data '{
#     "inputs": [
#         {
#             "data": [
#                 "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAAA10lEQVR4nGNgGFhgy6xVdrCszBaLFN/mr28+/QOCr69DMCSnA8WvHti0acu/fx/10OS0X/975CDDw8DA1PDn/1pBVEmLf3+zocy2X/+8USXt/82Ds+/+m4sqeehfOpw97d9VFDmlO++t4JwQNMm6f6sZcEpee2+DR/I4A05J7tt4JJP+IUsu+ncRp6TxO9RAQJY0XvrvMAuypNNHuCTz8n+PzVEcy3DtqgiY1ptx6t8/ewY0yX9ntoDA63//Xs3hQpMMPPsPAv68qmDAAFKXwHIzMzCl6AoAxXp0QujtP+8AAAAASUVORK5CYII="
#             ],
#             "datatype": "BYTES",
#             "name": "312a4eb0-0ca7-4803-a101-a6d2c18486fe",
#             "shape": [
#                 -1
#             ]
#         }
#     ]

# }'


## 返回

# {
#     "model_name": "mnist",
#     "model_version": null,
#     "id": "c9422f8f-7139-4974-9e30-17e2ec4c4562",
#     "parameters": null,
#     "outputs": [
#         {
#             "name": "312a4eb0-0ca7-4803-a101-a6d2c18486fe",
#             "shape": [
#                 1
#             ],
#             "datatype": "INT64",
#             "parameters": null,
#             "data": [
#                 0
#             ]
#         }
#     ]
# }

## 模型可解释
# curl --location 'http://************:30080/v2/models/mnist:explain' \




## 注意事项
# 1. 确保模型文件格式正确
# 2. 使用 V2 协议时，请求和响应格式与 V1 有显著差异


