# 推理服务部署指南

## 概述

本指南提供使用 Kubernetes 和 KServe 部署推理服务的说明，支持不同的模型部署场景。

## 部署配置

### 1. 主机路径自定义部署

文件：`custom-with-hostpath.yaml`

#### 特性
- 直接从主机挂载模型
- 支持 NVIDIA GPU
- 自定义模型服务器配置

#### 关键组件
- 使用主机路径卷存储模型
- 支持 NVIDIA 运行时类
- 可配置的模型服务器参数

#### 部署规格
- 镜像：`*************:3443/inference/amd64/qwen/qwen-omni:2.5-cu121-dev-xrwang-0408`
- 模型路径：`/home/<USER>/Qwen/Qwen2.5-Omni-7B`
- 服务器：vLLM OpenAI API 服务器
- GPU 资源：2 GPUs，4 CPU 核心，8Gi 内存

#### 部署示例
```bash
kubectl apply -f custom-with-hostpath.yaml
```

### 2. 模型仓库自定义部署

文件：`custom-with-modelhub.yaml`

#### 特性
- 从 CetcCloud 模型仓库检索模型
- 自动模型初始化
- NVIDIA GPU 支持

#### 关键组件
- 模型下载存储初始化器
- 基于密钥的认证
- 可配置的模型服务器参数

#### 部署规格
- 镜像：`*************:3443/inference/amd64/qwen/qwen-omni:2.5-cu121-dev-xrwang-0408`
- 模型来源：CetcCloud 模型仓库 (`cc://root/DeepSeek-7B`)
- 服务器：vLLM OpenAI API 服务器
- GPU 资源：2 GPUs，4 CPU 核心，8Gi 内存

#### 部署示例
```bash
kubectl apply -f custom-with-modelhub.yaml
```

## 模型服务器配置

### vLLM API 服务器参数

常用配置参数：
- `--model`：模型路径或仓库
- `--served-model-name`：模型标识符
- `--host`：绑定主机（默认：0.0.0.0）
- `--port`：服务端口（默认：8000）
- `--dtype`：模型数据类型（例如：bfloat16）
- `--tensor-parallel-size`：GPU 张量并行
- `--gpu-memory-utilization`：GPU 内存使用率

## 部署测试

### OpenAI 兼容性测试
```bash
curl -H "Content-Type: application/json" \
  http://<服务端点>/openai/v1/chat/completions \
  -d '{
    "model": "qwen-omni-7b",
    "messages": [
      {"role": "user", "content": "你好！"}
    ]
  }'
```

## 故障排除

1. 验证模型路径和权限
2. 检查 GPU 资源分配
3. 检查容器日志
4. 确保网络连接

## 最佳实践

- 使用适当的 GPU 资源
- 仔细配置模型路径
- 对敏感信息使用密钥
- 监控资源利用率

``` yaml
apiVersion: v1
data:
  CETCCLOUD_ENDPOINT: http://csghub.cetccloud.com:30080  ## 模型库地址
  CETCCLOUD_HUB_TOKEN: 428a020e513345bf84d0eb78aefa1b70 ## 模型库token ,公开仓库为 ""
kind: Secret
metadata:
  name: storage-config
  namespace: default
type: Opaque
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ccserviceacc
secrets:
  - name: storage-config
---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: cetccloud-deepseek
spec:
  predictor:
    serviceAccountName: ccserviceaccn
    nodeSelector:
      nvidia.com/gpu.product: Tesla-T4 ## 指定GPU类型,当使用GPU时，需要指定GPU类型
    runtimeClassName: nvidia   ## 当底层是k3s,且使用GPU时，需要指定runtimeClassName为nvidia
    model:
      protocolVersion: v1 ## 协议版本,默认为v1 ,可选值v1 v2 
      modelFormat:
        name: huggingface  ## 模型格式,当模型时llm文本生成模型的时候，需要指定模型格式为huggingface
      args:
      ###  model_name model_dir backend  tensor-parallel-size 必传字段 以及这些字段 只有modelFormat 为huggingface 才有这些必传字段
        - --model_name=deepseek  ## 模型名称 必填字段
        - --model_dir=/mnt/models/root/DeepSeek-R1-Distill-Qwen-1.5B/  ## 模型路径 必填字段
        - --backend=vllm  ## 模型后端 必填字段 当前只支持vllm  后期会支持sglang 
        - --tensor-parallel-size=1  ## 模型并行数,和卡的数量有关,默认为用户选多少张卡 必填字段
        - --enable_docs_url=True  ## 开启Enable Swagger Ui 当前暂定为必填字段 默认为True


      ### 用户可追加字段 key value 结构
        - --dtype=half  ## 模型精度 
        - --gpu_memory_utilization=0.95  ## gpu最大利用率,默认为0.9
        - --enable-chunked-prefill=False  ## 是否启用分块预填充 当gpu 卡类型为图turing 时，需要设置为False
        - --max_length=8092  ## 最大长度

      ### 模型存储路径,支持类型有pvc、s3、cc、url 
      ###  cc 代表cetccloudhub 模型库
      ###  pvc 代表pvc存储
      ###  s3 代表s3存储
      ###  url 代表url存储
      ###  cc 代表 cetccloudhub 模型库, 格式为 cc://${REPO}/${MODEL}:${HASH}(optional) ,案例 cc://root/DeepSeek-R1-Distill-Qwen-1.5B:main
      storageUri: cc://root/DeepSeek-R1-Distill-Qwen-1.5B:main  ## 模型存储路径,当使用模型库时，需要指定模型存储路径,
      resources:
        limits:
          cpu: "6"
          memory: 24Gi
          nvidia.com/gpu: "1"
        requests:
          cpu: "6"
          memory: 24Gi
          nvidia.com/gpu: "1"



```

``` bash 
### 根据InferenceService 的url 访问模型, READY 为服务的状态 true 是模型启动好,false 为false ,当部署环境为机动云环境的时,为最小化部署,PREV LATEST PREVROLLEDOUTREVISION  LATESTREADYREVISION 为空
[root@node01 huazq]# kubectl get isvc
NAME                 URL                                             READY   PREV   LATEST   PREVROLLEDOUTREVISION   LATESTREADYREVISION   AGE
cetccloud-deepseek   http://cetccloud-deepseek-default.example.com   True                                                                  36 



```
###  OpenAI Chat Completions streaming request:

``` bash

curl -H "content-type:application/json" -H "Host: ${SERVICE_HOSTNAME}" \
-v http://${INGRESS_HOST}:${INGRESS_PORT}/openai/v1/chat/completions \
-d '{"model":"llama3","messages":[{"role":"system","content":"You are an assistant that speaks like Shakespeare."},{"role":"user","content":"Write a poem about colors"}],"max_tokens":30,"stream":true}'

```

```bash
[root@node01 huazq]# kubectl get svc  -n csghub  | grep csghub-ingress-nginx-controller 
csghub-ingress-nginx-controller             NodePort    *************   <none>        80:30080/TCP,443:30443/TCP,22:30022/TCP   46h
csghub-ingress-nginx-controller-admission   ClusterIP   *************   <none>        443/TCP                                   46h

```
### *********** 测试例子

``` bash
curl --location 'http://***********:30080/openai/v1/chat/completions' \ ### ing
--header 'Host: cetccloud-deepseek-default.example.com' \ 
--header 'Content-Type: application/json' \
--data '{
    "model": "deepseek", ### 模型名称
    "messages": [
        {
            "role": "system",
            "content": "You are a helpful assistant."
        },
        {
            "role": "user",
            "content": "Hello!"
        }
    ]
}'
```


### Swagger Ui 访问地址

``` bash
http://${INGRESS_HOST}:${INGRESS_PORT}/docs
```


