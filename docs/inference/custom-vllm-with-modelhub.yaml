apiVersion: v1
data:
  CETCCLOUD_ENDPOINT: aHR0cDovLzEwLjIwMC45LjI0OTozMDA4MA==  ## 模型库地址
  CETCCLOUD_HUB_TOKEN: aHR0cDovLzEwLjIwMC45LjI0OTozMDA4MA== ## 模型库token ,公开仓库为 ""
kind: Secret
metadata:
  name: storage-config
  namespace: default
type: Opaque
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ccserviceacc
secrets:
  - name: storage-config
---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: cetccloud-custom-deploy    # 推理服务的名称，用户可自定义
  annotations:
    "serving.kserve.io/enable-nodeport": "true"  
    "nginx.ingress.kubernetes.io/enable-cors": "true"
spec:
  predictor:
    runtimeClassName: nvidia       # 当底层是k3s且使用GPU时，需要指定runtimeClassName为nvidia
    containers:
    - image: 172.28.20.118:3443/inference/amd64/qwen/qwen-omni:2.5-cu121-dev-xrwang-0408    # 推理服务使用的镜像 ,用户自定义 ,内部镜像地址  hub.cetccloud.io:5000/onestack/inferece/qwen/qwen-omni:2.5-cu121
      name: kserve-container       # 容器名称
      workingDir: /code      # 容器的默认工作目录 ,默认为/code,支持用户传入
      command: ["/bin/bash", "-c"] ## 这里的/bin/bash是容器启动命令，用户需要根据实际情况填写, 也许镜像是/bin/sh
      args:
      - python -m vllm.entrypoints.openai.api_server
        --model /mnt/models
        --served-model-name "qwen-omni-7b"
        --host 0.0.0.0
        --port 8000
        --dtype bfloat16
        --tensor-parallel-size 2
        --gpu-memory-utilization 0.95
      ports:
        - containerPort: 8000    # 服务端口号，确保与您的应用监听端口一致
      resources:                 # 资源限制配置
        limits:                  # 资源上限配置
          cpu: "24"              # CPU核心数
          memory: 48Gi           # 内存大小
          nvidia.com/gpu: "2"   # GPU数量
        requests:               # 资源请求配置
          cpu: "24"             # 最小CPU需求
          memory: 48Gi          # 最小内存需求
          nvidia.com/gpu: "2"  # 最小GPU需求
      volumeMounts:            # 数据卷挂载配置
      - mountPath: /code  # 容器内挂载路径
        name: mount-code       # 挂载的数据卷名称
      - mountPath: /dev/shm  ## 支持用户挂载共享内存,可选,当用户选择多张卡时，建议挂载共享内存
        name: dshm
      - mountPath: /mnt/models
        name: model-volume
    ## 支持用户挂载代码,可选    
    initContainers:        
    - args:                   
      - sh
      - -c
      - set -e;git clone http://172.28.1.130/wangxingrui/Qwen2.5-Omni-Server.git -b master;ls -l    # 克隆代码仓库
      image: 172.28.20.118:3443/inference/amd64/git:2.34.5       # Git操作镜像 ,内部镜像地址 hub.cetccloud.io:5000/onestack/git:2.34.5
      imagePullPolicy: IfNotPresent                              # 镜像拉取策略
      name: code-init                                            # 初始化容器名称
      resources: {}                                              # 初始化容器资源配置
      workingDir: /code                                          # 初始化容器工作目录 ,可选 默认为/workspace
      volumeMounts:                                              # 初始化容器数据卷挂载
      - mountPath: /code                                         # 初始化容器挂载路径
        name: mount-code                                         # 挂载的数据卷名称
    #  支持用户挂载模型库模型,可选
    - name: storage-initializer 
      image: 172.28.20.118:3443/ai-platform/inference/amd64/kserve/storage-initializer:v0.14.1  ## 内部镜像地址 hub.cetccloud.io:5000/onestack/inferece/kserve/storage-initializer:v0.14.1
      args:
        - cc://root/Qwen2.5-Omni-7B  # 模型库模型路径
        - /mnt/models          # 模型挂载路径
      resources: 
        limits:
          cpu: 1
          memory: 1Gi
        requests:
          cpu: 100m
          memory: 100Mi
      env:
        - name: CETCCLOUD_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: storage-config
              key: CETCCLOUD_ENDPOINT
        - name: CETCCLOUD_HUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: storage-config
              key: CETCCLOUD_HUB_TOKEN
      volumeMounts:
      - mountPath: /mnt/models
        name: model-volume
    volumes:                                                     # 数据卷定义
    - emptyDir: {}                                              # 使用临时存储卷
      name: mount-code                                          # 数据卷名称
    # 新增模型数据卷
    - name: model-volume
      emptyDir: {}
    - name: dshm ## 支持用户挂载共享内存,可选,当用户选择多张卡时，建议挂载共享内存
      emptyDir:
        medium: Memory
        sizeLimit: 64Gi





# curl -X POST 10.200.9.249:267/v1/chat/completions \
# -H "Authorization: Bearer $DASHSCOPE_API_KEY" \
# -H "Content-Type: application/json" \
# -d '{
#     "model": "qwen-omni-7b",
#     "messages": [
#    {
#       "role": "system",
#       "content": [{"type":"text","text": "You are a helpful assistant."}]},
#     {
#       "role": "user",
#       "content": [
#         {
#           "type": "image_url",
#           "image_url": {
#             "url": "https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241022/emyrja/dog_and_girl.jpeg"
#           }
#         },
#         {
#           "type": "text",
#           "text": "图中描绘的是什么景象？"
#         }
#       ]
#     }
#   ],
#     "stream":true,
#     "stream_options":{
#         "include_usage":true
#     },
#     "modalities":["text"]
# }'
