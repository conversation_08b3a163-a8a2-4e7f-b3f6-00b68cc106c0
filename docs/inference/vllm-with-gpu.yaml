apiVersion: v1
data:
  CETCCLOUD_ENDPOINT: http://csghub.cetccloud.com:30080  ## 模型库地址
  CETCCLOUD_HUB_TOKEN: 428a020e513345bf84d0eb78aefa1b70 ## 模型库token ,公开仓库为 “”
kind: Secret
metadata:
  name: storage-config
  namespace: default
type: Opaque
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ccserviceacc
secrets:
  - name: storage-config
---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  name: cetccloud-deepseek
  annotations:
    serving.kserve.io/enable-nodeport: true ## 开启nodeport服务
    nginx.ingress.kubernetes.io/enable-cors: true ## 开启cors
spec:
  predictor:
    nodeSelector:
      nvidia.com/gpu.product: Tesla-T4 ## 指定GPU类型,当使用GPU时，需要指定GPU类型
    serviceAccountName: ccserviceaccn
    runtimeClassName: nvidia   ## 当底层是k3s,且使用GPU时，需要指定runtimeClassName为nvidia
    model:
      protocolVersion: v1 ## 协议版本,默认为v1 ,可选值v1 v2 
      modelFormat:
        name: huggingface  ## 模型格式,当模型时llm文本生成模型的时候，需要指定模型格式为huggingface
      args:
      ###  model_name model_dir backend  tensor-parallel-size 必传字段
        - --model_name=deepseek  ## 模型名称 必填字段
        - --model_dir=/mnt/models/root/DeepSeek-R1-Distill-Qwen-1.5B/  ## 模型路径 必填字段
        - --backend=vllm  ## 模型后端 必填字段 当前只支持vllm  后期会支持sglang
        - --tensor-parallel-size=1  ## 模型并行数,和卡的数量有关,默认为用户选多少张卡 必填字段
        - --enable_docs_url=True  ## 开启Enable Swagger Ui 当前暂定为必填字段 默认为True


      ### 用户可追加字段 key value 结构
        - --dtype=half  ## 模型精度 
        - --gpu_memory_utilization=0.95  ## gpu最大利用率,默认为0.9
        - --enable-chunked-prefill=False  ## 是否启用分块预填充 当gpu 卡类型为图turing 时，需要设置为False
        - --max_length=8092  ## 最大长度

      ### 模型存储路径,支持类型有pvc、s3、cc、url 
      ###  cc 代表cetccloudhub 模型库
      ###  pvc 代表pvc存储
      ###  s3 代表s3存储
      ###  url 代表url存储
      ###  cc 代表 cetccloudhub 模型库, 格式为 cc://${REPO}/${MODEL}:${HASH}(optional) ,案例 cc://root/DeepSeek-R1-Distill-Qwen-1.5B:main
      storageUri: cc://root/DeepSeek-R1-Distill-Qwen-1.5B:main  ## 模型存储路径,当使用模型库时，需要指定模型存储路径,
      resources:
        limits:
          cpu: "6"
          memory: 24Gi
          nvidia.com/gpu: "1"
        requests:
          cpu: "6"
          memory: 24Gi
          nvidia.com/gpu: "1"
