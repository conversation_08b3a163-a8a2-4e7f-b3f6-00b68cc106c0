# cetccloud-operator

## 项目描述

cetccloud-operator 提供了对 Notebook 等自定义资源的管理能力，支持资源的创建、更新、删除等生命周期管理。通过该控制器，用户可以方便地在 Kubernetes 集群中部署和管理 Jupyter Notebook 等资源。

## 功能特性

- 支持 Notebook 自定义资源的全生命周期管理
- 自动创建和管理 StatefulSet、Service 等相关资源
- 支持 Notebook 状态监控和事件记录
- 支持通过 NodePort 方式访问 Notebook 服务
- 支持代码仓库挂载和数据源配置
- 支持资源的启停控制（通过注解实现）

## 快速开始

### 前置条件

- Kubernetes 集群 (版本 >= 1.19)
- kubectl 命令行工具
- Go 开发环境 (版本 >= 1.19)

### 安装部署

1. 克隆代码仓库
```bash
git clone https://github.com/cetccloud/cetccloud-operator.git
cd cetccloud-operator
```

2. 安装 CRD
```bash
make manifests
kubectl apply -f config/crd/bases
```

3. 部署控制器
```bash
make deploy
```

### 启动项目

#### 开发环境启动

在开发环境中，您可以通过以下方式启动控制器：

1. 命令行启动：
```bash
# 关闭leader选举模式，适合本地开发
make run ARGS="--leader-elect=false"

# 或直接使用go run启动
go run ./cmd/controller-manager/controller-manager.go --leader-elect=false
```

2. 使用 VSCode 调试启动：
   - 在 VSCode 中打开项目
   - 创建 `.vscode/launch.json` 文件，内容如下：
   ```json
   {
       "version": "0.2.0",
       "configurations": [
           {
               "name": "Launch Package",
               "type": "go",
               "request": "launch",
               "mode": "auto",
               "program": "${workspaceFolder}/cmd/controller-manager/controller-manager.go",
               "args": ["--leader-elect=false"]
           }
       ]
   }
   ```
   - 按 F5 或点击调试图标启动调试会话

#### 生产环境部署

对于生产环境，建议以下部署方式：

```bash
# 构建镜像
make docker-build

# 推送镜像
make docker-push

# 部署到集群
make deploy
```

### 使用示例

#### 创建 Notebook 实例

1. 创建一个简单的 Notebook 实例：
```bash
kubectl apply -f examples/jupyter.yaml
```

示例配置：
```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  labels:
    app: demo
  name: demo
spec:
  template:
    spec:
      containers:
      - env: []
        image: kubeflownotebookswg/jupyter-scipy:v1.8.0
        imagePullPolicy: IfNotPresent
        name: deeeee
        resources:
          limits:
            cpu: "0.6"
            memory: 1.2Gi
          requests:
            cpu: "0.5"
            memory: 1Gi
```

2. 查看 Notebook 状态：
```bash
kubectl get notebooks
kubectl describe notebook demo
```

3. 停止/启动 Notebook（通过注解控制）：
```bash
# 通过添加停止注解来设置停止时间
kubectl annotate notebook demo cetccloud.ai/stop-nb="2025-03-24T08:31:47Z" --overwrite

# 或者应用带有停止注解的YAML文件
kubectl apply -f examples/jupyter-with-stop.yaml
```

示例配置（带停止注解）：
```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  annotations:
    cetccloud.ai/stop-nb: "2025-03-24T08:31:47Z"  # 设置停止时间
  labels:
    app: demo
  name: demo
spec:
  # ...其他配置与jupyter.yaml相同
```

4. 移除停止注解以重新启动 Notebook：
```bash
kubectl annotate notebook demo cetccloud.ai/stop-nb-
```

### Notebook CRD 配置说明

Notebook CRD 支持以下配置：

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: Notebook
metadata:
  name: jupyter-sample
  annotations:
    cetccloud.ai/stop-nb: "2023-12-31T23:59:59Z"   # 可选，指定停止时间
spec:
  template:
    # 设置Notebook最大运行时间（分钟）和重试次数
    maxRunTime: 60                                # 可选，最大运行时间（分钟）
    maxRetryCount: 3                              # 可选，最大重试次数


    # Kubernetes Pod 规格配置
    spec:
      containers:
      - name: jupyter
        image: kubeflownotebookswg/jupyter-scipy:v1.8.0
        resources:
          limits:
            cpu: "1"
            memory: 2Gi
          requests:
            cpu: "0.5"
            memory: 1Gi
        volumeMounts:
        - mountPath: /home/<USER>
          name: workspace
      volumes:
      - name: workspace
        persistentVolumeClaim:
          claimName: jupyter-workspace
```

## 控制器配置参数说明

`cetccloud-operator` 支持以下命令行参数，用于自定义控制器的行为：

### 领导者选举相关参数

```
--leader-elect=true|false
  启用领导者选举。在高可用部署中运行多副本时启用此选项。
  默认值：true

--namespace=<namespace>
  用于存储领导者选举 ConfigMap 的命名空间。
  默认值：kube-system

--leader-elect-lease-duration=<duration>
  领导者租约的持续时间。
  默认值：15s

--leader-elect-renew-deadline=<duration>
  领导者续约的最后期限。
  默认值：10s

--leader-elect-retry-period=<duration>
  尝试获取或更新领导权的重试周期。
  默认值：2s
```

### 指标相关参数

```
--metrics-bind-address=<address>
  Prometheus 指标服务器的绑定地址。
  格式：[host]:port
  默认值：:8080

--metrics-secure-serving=true|false
  启用指标服务器的身份验证和授权。
  默认值：false

--metrics-enable-http2=true|false
  启用指标服务器的 HTTP/2 支持。
  默认值：true
```

### 控制器选择参数

```
--controllers=<list>
  要启用的控制器列表。
  - '*' 启用所有默认控制器
  - 'notebook' 仅启用 notebook 控制器
  - '-notebook' 禁用 notebook 控制器
  默认值：*
```

### 日志相关参数

```
--v=<level>
  日志详细程度级别。
  - 0：基本日志
  - 2：更详细的日志
  - 4：调试级日志
  默认值：0

--log-dir=<dir>
  日志文件的存储目录。
  默认：标准输出
```

## 开发指南

### 添加新功能

1. 修改 API 定义（api/v1alpha1/notebook_types.go）
2. 生成代码：`make generate`
3. 更新 CRD：`make manifests`
4. 实现控制器逻辑
5. 运行测试确保功能正常



## 项目结构

```
├── api/                    # API 定义
├── cmd/                    # 主程序入口
├── config/                 # 配置文件
├── examples/               # 使用示例
├── internal/               # 内部实现
└── test/                   # 测试代码
```

## Makefile 使用指南

Makefile 提供了一系列便捷的命令，帮助开发者快速进行项目构建、测试、部署和开发。以下是主要的 Makefile 目标（targets）及其用途：

### 开发相关命令

- `make generate`: 生成代码，包括 DeepCopy 方法实现
  ```bash
  make generate
  ```

- `make manifests`: 生成 CRD（自定义资源定义）、WebhookConfiguration 和 ClusterRole
  ```bash
  make manifests
  ```

- `make fmt`: 使用 `go fmt` 格式化代码
  ```bash
  make fmt
  ```

- `make vet`: 运行 `go vet` 进行静态代码分析
  ```bash
  make vet
  ```

- `make test`: 运行单元测试
  ```bash
  make test
  ```

- `make test-e2e`: 运行端到端测试（需要预先配置 Kind 集群）
  ```bash
  make test-e2e
  ```

### 构建相关命令

- `make build`: 编译项目可执行文件
  ```bash
  make build
  ```

- `make run`: 直接在本地运行控制器
  ```bash
  make run
  ```

- `make docker-build`: 构建 Docker 镜像
  ```bash
  make docker-build
  ```

- `make docker-push`: 推送 Docker 镜像到仓库
  ```bash
  make docker-push
  ```

- `make docker-buildx`: 构建多平台 Docker 镜像
  ```bash
  make docker-buildx
  ```

### 部署相关命令

- `make install`: 在 Kubernetes 集群中安装 CRD
  ```bash
  make install
  ```

- `make uninstall`: 从 Kubernetes 集群中卸载 CRD
  ```bash
  make uninstall
  ```

- `make deploy`: 部署控制器到 Kubernetes 集群
  ```bash
  make deploy
  ```

- `make undeploy`: 从 Kubernetes 集群中移除控制器
  ```bash
  make undeploy
  ```

### 高级用法

可以通过设置环境变量来自定义构建和部署：

- `IMG`: 指定镜像名称和标签
  ```bash
  IMG=your-registry/cetccloud-operator:v1.0.0 make docker-build
  ```

- `PLATFORMS`: 指定多平台构建的目标平台
  ```bash
  PLATFORMS=linux/amd64,linux/arm64 make docker-buildx
  ```

### 常见问题排查

- 使用 `make help` 查看所有可用的 Makefile 目标
- 确保已安装 Go、Docker 和 Kubernetes 相关工具
- 检查 `config/` 目录下的配置文件是否正确

注意：某些命令可能需要特定的环境配置，如 Kubernetes 集群访问权限、Docker 构建环境等。


