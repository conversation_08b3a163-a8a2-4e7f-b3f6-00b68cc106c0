package api

import (
	"testing"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"

	// Third-party imports
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var scheme = runtime.NewScheme()

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
}

func Test_SetupAllBackends(t *testing.T) {
	c := fake.NewClientBuilder().WithScheme(scheme).Build()
	SetupAllBackends(c, scheme)
	GetServiceBackend()
	GetDeployBackend()
	GetGatewayBackend()
}
