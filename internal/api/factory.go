package api

import (
	"cetccloud/cetccloud-operator/internal/component/assembly"
	"cetccloud/cetccloud-operator/internal/component/deploy"
	"cetccloud/cetccloud-operator/internal/component/empty"
	"cetccloud/cetccloud-operator/internal/component/gateway"
	"cetccloud/cetccloud-operator/internal/component/service"
	"cetccloud/cetccloud-operator/internal/constants"

	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var (
	DeployFactory  = make(map[string]func() BackendInterface)
	ServiceFactory = make(map[string]func() BackendInterface)
	GatewayFactory = make(map[string]func() BackendInterface)
)

func GetDeployBackend() BackendInterface {
	if f, found := DeployFactory[constants.DefaultBackend]; found {
		return f()
	}
	return &empty.EmptyBackend{}
}

func GetServiceBackend() BackendInterface {
	if f, found := ServiceFactory[constants.DefaultBackend]; found {
		return f()
	}
	return &empty.EmptyBackend{}
}

func GetGatewayBackend() BackendInterface {
	if f, found := GatewayFactory[constants.DefaultBackend]; found {
		return f()
	}
	return &empty.EmptyBackend{}
}

func SetupAllBackends(client client.Client, scheme *runtime.Scheme) {
	ServiceFactory[constants.DefaultBackend] = func() BackendInterface {
		svc := &service.Service{}
		svc.Setup(client, scheme)
		return svc
	}

	DeployFactory[constants.DefaultBackend] = func() BackendInterface {
		deploy := &deploy.Statefulset{
			Builder: assembly.NewKubernetesBuilder().DeployBuilder(),
		}
		deploy.Setup(client, scheme)
		return deploy
	}

	GatewayFactory[constants.DefaultBackend] = func() BackendInterface {
		gateway := &gateway.IngressNginxBackend{}
		gateway.Setup(client, scheme)
		return gateway
	}
}
