package constants

type GatewayBackend string

const (
	NginxGateway GatewayBackend = "nginx"
	// KongGateway   GatewayBackend = "kong"
	// APISixGateway GatewayBackend = "apisix"
	// K8sGateway    GatewayBackend = "k8s"
)

const (
	DefaultBackend = "default"
)

const (
	LabelNotebookName           = "cetccloud.ai/notebook-name"
	LabelStatefulsetName        = "cetccloud.ai/statefulset-name"
	AnnotationStorageSize       = "cetccloud.ai/storage-size"
	AnnotationStorageAutoDelete = "cetccloud.ai/storage-auto-delete"
	AnnotationStorageClass      = "cetccloud.ai/storage-class"
	PodIndexField               = ".metadata.labels['" + LabelNotebookName + "']"
)

const (
	DefaultContainerPort = 8888
	DefaultServingPort   = 80
	DefaultSSHPort       = 22
	DefaultFSGroup       = int64(100)
	DefaultStorageSize   = "50Gi"
	DefaultStorageName   = "workspace"
	DefaultWorkingDir    = "/home/<USER>"
	PrefixEnvVar         = "NB_PREFIX"
	NBHomeEnvVar         = "HOME"
	STOP_ANNOTATION      = "cetccloud.ai/stop-nb"
)

type NotebookState string

const (
	NotebookStateCreating NotebookState = "Creating"

	NotebookStateRunning NotebookState = "Running"

	NotebookStateStopping NotebookState = "Stopping"

	NotebookStateStopped     NotebookState = "Stopped"
	NotebookStateTerminating               = "Terminating"
	NotebookStateFailed      NotebookState = "Failed"

	NotebookStateUnKnown NotebookState = ""
)

const (
	CreatingAppMsg  = "Creating NoteBook"
	RestaringAppMsg = "Restarting NoteBook"
	RunningAppMsg   = "NoteBook Running"
	StoppingAppMsg  = "Stopping NoteBook"
	StoppedAppMsg   = "NoteBook Stopped"
	ListeningAppMsg = "NoteBook Listening"
	FailedAppMsg    = "NoteBook Failed"
)
