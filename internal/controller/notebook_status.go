package controller

import (
	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
	"context"
	"fmt"
	"reflect"
	"time"

	// Third-party imports
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

func (n *NotebookReconciler) findPodsForNoteBook(ctx context.Context, object client.Object) []reconcile.Request {
	pod := object.(*corev1.Pod)
	var requests []reconcile.Request
	if pod.Labels[constants.LabelNotebookName] != "" {
		requests = []reconcile.Request{
			{
				NamespacedName: types.NamespacedName{
					Namespace: pod.Namespace,
					Name:      pod.Labels[constants.LabelNotebookName],
				},
			},
		}
	}
	return requests
}

func (n *NotebookReconciler) updateNotebookStatus(notebook *systemv1alpha1.Notebook,
	sts *appsv1.StatefulSet, pod *corev1.Pod) error {
	log := n.Log.WithValues(
		"namespace", notebook.Namespace,
		"name", notebook.Name,
	)
	ctx := context.Background()

	status, err := n.createNotebookStatus(notebook, sts, pod)
	if err != nil {
		return err
	}

	log.Info("Updating Notebook CR Status", "status", fmt.Sprintf("%+v", status))
	notebook.Status = status
	return n.Status().Update(ctx, notebook)
}

func (n *NotebookReconciler) createNotebookStatus(notebook *systemv1alpha1.Notebook,
	sts *appsv1.StatefulSet, pod *corev1.Pod) (systemv1alpha1.NotebookStatus, error) {

	log := n.Log.WithValues(
		"namespace", notebook.Namespace,
		"name", notebook.Name,
	)
	log.Info("Initializing Notebook CR Status")

	var readyReplicas int32 = 0
	var conditions []systemv1alpha1.NotebookCondition

	if sts == nil {
		conditions = []systemv1alpha1.NotebookCondition{
			{
				Type:               "Ready",
				Status:             "False",
				LastTransitionTime: metav1.Now(),
				LastProbeTime:      metav1.Now(),
				Reason:             "Creating",
				Message:            "StatefulSet is not created yet",
			},
		}
	} else {
		readyReplicas = sts.Status.ReadyReplicas
		conditions = make([]systemv1alpha1.NotebookCondition, 0)
	}

	status := systemv1alpha1.NotebookStatus{
		Conditions:     conditions,
		ReadyReplicas:  readyReplicas,
		ContainerState: corev1.ContainerState{},
		NotebookState:  notebook.Status.NotebookState,
	}

	// Update the status based on the Pod's status
	if reflect.DeepEqual(pod.Status, corev1.PodStatus{}) {
		log.Info("No pod.Status found. Won't update notebook conditions and containerState")
		return status, nil
	}

	// Update status of the CR using the ContainerState of
	// the container that has the same name as the CR
	notebookContainerFound := false
	log.Info("Calculating Notebook's containerState")
	for i := range pod.Status.ContainerStatuses {
		containerName := pod.Status.ContainerStatuses[i].Name
		log.Info("Checking container", "containerName", containerName)

		if containerName == "notebook" || containerName == notebook.Name {
			cs := pod.Status.ContainerStatuses[i].State
			log.Info("Found notebook container, updating state", "containerState", cs)

			status.ContainerState = cs
			notebookContainerFound = true
			break
		}
	}

	if !notebookContainerFound {
		log.Info("Could not find notebook container in Pod's containerStates",
			"availableContainers", getContainerNames(pod.Status.ContainerStatuses),
			"expectedName", "notebook")
	}

	// Mirroring pod conditions
	notebookConditions := []systemv1alpha1.NotebookCondition{}
	log.Info("Calculating Notebook's Conditions")
	for i := range pod.Status.Conditions {
		condition := n.PodCondToNotebookCond(pod.Status.Conditions[i])
		notebookConditions = append(notebookConditions, condition)
	}

	status.Conditions = notebookConditions

	return status, nil
}

// 辅助函数，用于获取所有容器名称
func getContainerNames(containerStatuses []corev1.ContainerStatus) []string {
	names := make([]string, len(containerStatuses))
	for i, status := range containerStatuses {
		names[i] = status.Name
	}
	return names
}

func (n *NotebookReconciler) PodCondToNotebookCond(podc corev1.PodCondition) systemv1alpha1.NotebookCondition {
	condition := systemv1alpha1.NotebookCondition{}

	if len(podc.Type) > 0 {
		condition.Type = string(podc.Type)
	}

	if len(podc.Status) > 0 {
		condition.Status = string(podc.Status)
	}

	if len(podc.Message) > 0 {
		condition.Message = podc.Message
	}

	if len(podc.Reason) > 0 {
		condition.Reason = podc.Reason
	}

	// check if podc.LastProbeTime is null. If so initialize
	// the field with metav1.Now()
	check := podc.LastProbeTime.Time.Equal(time.Time{})
	if !check {
		condition.LastProbeTime = podc.LastProbeTime
	} else {
		condition.LastProbeTime = metav1.Now()
	}

	// check if podc.LastTransitionTime is null. If so initialize
	// the field with metav1.Now()
	check = podc.LastTransitionTime.Time.Equal(time.Time{})
	if !check {
		condition.LastTransitionTime = podc.LastTransitionTime
	} else {
		condition.LastTransitionTime = metav1.Now()
	}

	return condition
}
