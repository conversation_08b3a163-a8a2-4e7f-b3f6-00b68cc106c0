/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	"github.com/go-logr/logr"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"
)

// NotebookImageSaver related constants
const (
	// ImageSaverJobPrefix is the prefix for image saver job names
	ImageSaverJobPrefix     = "image-saver"
	CRISocketVolumeName     = "cri-socket"
	NerdctlConfigVolumeName = "nerdctl-config"
	RegistryCAVolumeName    = "registry-ca"

	NerdctlVolumePath = "/etc/nerdctl/nerdctl.toml"
)

// NotebookImageSaverReconciler reconciles a NotebookImageSaver object
type NotebookImageSaverReconciler struct {
	client.Client
	logger        logr.Logger
	Scheme        *runtime.Scheme
	EventRecorder record.EventRecorder
}

// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers/finalizers,verbs=update
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks,verbs=get;list;watch
// +kubebuilder:rbac:groups=batch,resources=jobs,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
// +kubebuilder:rbac:groups="",resources=events,verbs=create;patch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *NotebookImageSaverReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	r.logger = log.FromContext(ctx, "image-saver", req.Name)
	_ = log.IntoContext(ctx, r.logger)
	r.logger.Info("reconciling image-saver")

	// Fetch the NotebookImageSaver instance
	imageSaver := &systemv1alpha1.NotebookImageSaver{}
	if err := r.Get(ctx, req.NamespacedName, imageSaver); err != nil {
		if k8serrors.IsNotFound(err) {
			r.logger.Info("NotebookImageSaver resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		r.logger.Error(err, "Failed to get NotebookImageSaver")
		return ctrl.Result{}, err
	}

	// Process the image saver based on its current phase
	return r.processImageSaver(ctx, imageSaver)
}

func (r *NotebookImageSaverReconciler) updateAppStatus(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	logger.Info("updateNoteBookImageSaverStatus", "saver", imageSaver.Name, "status", imageSaver.Status.Phase)
	if e := r.Status().Update(ctx, imageSaver); e != nil && k8serrors.IsConflict(e) {
		return reconcile.Result{Requeue: true}, nil
	}
	return ctrl.Result{}, nil
}

// processImageSaver processes the image saver based on its current phase
func (r *NotebookImageSaverReconciler) processImageSaver(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	switch imageSaver.Status.Phase {
	case "":
		// Initialize the image saver
		return r.initializeImageSaver(ctx, imageSaver)
	case systemv1alpha1.NotebookImageSaverPhasePending:
		// Create the job
		return r.createImageSaverJob(ctx, imageSaver)
	case systemv1alpha1.NotebookImageSaverPhaseRunning:
		// Monitor the job
		return r.monitorImageSaverJob(ctx, imageSaver)
	case systemv1alpha1.NotebookImageSaverPhaseSucceeded, systemv1alpha1.NotebookImageSaverPhaseFailed:
		// Job completed, no further action needed
		r.logger.Info("Image saver completed", "phase", imageSaver.Status.Phase)
		return ctrl.Result{}, nil
	default:
		r.logger.Info("Unknown phase", "phase", imageSaver.Status.Phase)
		return ctrl.Result{}, nil
	}
}

// initializeImageSaver initializes the image saver and gathers necessary information
func (r *NotebookImageSaverReconciler) initializeImageSaver(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	notebook := &systemv1alpha1.Notebook{}
	err := r.Get(ctx, types.NamespacedName{
		Name:      imageSaver.Spec.NotebookName,
		Namespace: imageSaver.Namespace,
	}, notebook)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "NotebookNotFound",
				fmt.Sprintf("Notebook %s not found", imageSaver.Spec.NotebookName))
			imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
			imageSaver.Status.Message = fmt.Sprintf("Notebook %s not found", imageSaver.Spec.NotebookName)
			r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "NotebookNotFound", fmt.Sprintf("Notebook %s not found", imageSaver.Spec.NotebookName))
			return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
		}
		r.logger.Error(err, "Failed to get notebook")
		return ctrl.Result{}, err
	}

	// Check if notebook is running
	if notebook.Status.NotebookState != constants.NotebookStateRunning {
		r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "NotebookNotRunning",
			"Notebook is not in running state")
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
		imageSaver.Status.Message = "Notebook is not in running state"
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "NotebookNotRunning", "Notebook is not in running state")
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	}

	// Get the pod
	pod := &corev1.Pod{}
	err = r.Get(ctx, types.NamespacedName{
		Name:      notebook.Name + "-0", // StatefulSet pod naming convention
		Namespace: imageSaver.Namespace,
	}, pod)
	if err != nil {
		r.logger.Error(err, "Failed to get notebook pod")
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
		imageSaver.Status.Message = "Failed to get notebook pod"
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "PodNotFound", "Failed to get notebook pod")
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	}

	// Find the container and get its ID
	var containerID string
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Name == constants.NotebookContainerName {
			containerID = containerStatus.ContainerID
			break
		}
	}

	if containerID == "" {
		r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "ContainerNotFound",
			fmt.Sprintf("Container %s not found in pod", constants.NotebookContainerName))
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
		imageSaver.Status.Message = fmt.Sprintf("Container %s not found in pod", constants.NotebookContainerName)
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "ContainerNotFound", fmt.Sprintf("Container %s not found in pod", constants.NotebookContainerName))
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	}

	// Update status with gathered information
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhasePending
	imageSaver.Status.NodeName = pod.Spec.NodeName
	imageSaver.Status.ContainerID = containerID
	imageSaver.Status.StartTime = &metav1.Time{Time: time.Now()}
	imageSaver.Status.Message = "Initializing image saver"

	// Update conditions
	r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "Initializing", "Image saver is being initialized")

	// Set owner reference
	if err := controllerutil.SetControllerReference(notebook, imageSaver, r.Scheme); err != nil {
		r.logger.Error(err, "Failed to set controller reference")
		return ctrl.Result{}, err
	}

	r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "Initialized", "Image saver initialized")
	return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
}

// createImageSaverJob creates a job to save the notebook image
func (r *NotebookImageSaverReconciler) createImageSaverJob(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	jobName := fmt.Sprintf("%s-%s", ImageSaverJobPrefix, imageSaver.Name)

	// Check if job already exists
	existingJob := &batchv1.Job{}
	err := r.Get(ctx, types.NamespacedName{Name: jobName, Namespace: imageSaver.Namespace}, existingJob)
	if err == nil {
		// Job already exists, update status and move to running phase
		imageSaver.Status.JobName = jobName
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseRunning
		imageSaver.Status.Message = "Image saver job is running"
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionTrue, "JobExists", "Image saver job already exists")
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	} else if !k8serrors.IsNotFound(err) {
		logger.Error(err, "Failed to get existing job")
		return ctrl.Result{}, err
	}

	// Create the job
	job := r.buildImageSaverJob(imageSaver, jobName)

	// Set owner reference
	if err := controllerutil.SetControllerReference(imageSaver, job, r.Scheme); err != nil {
		logger.Error(err, "Failed to set controller reference")
		return ctrl.Result{}, err
	}

	if err := r.Create(ctx, job); err != nil {
		logger.Error(err, "Failed to create image saver job")
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
		imageSaver.Status.Message = "Failed to create image saver job"
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionFalse, "JobCreationFailed", err.Error())
		r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "JobCreationFailed", err.Error())
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	}

	// Update status
	imageSaver.Status.JobName = jobName
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseRunning
	imageSaver.Status.Message = "Image saver job created and running"

	// Update conditions
	r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionTrue, "JobCreated", "Image saver job created successfully")

	r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "JobCreated", "Image saver job created")
	return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
}

// buildImageSaverJob builds the job specification for saving the notebook image
func (r *NotebookImageSaverReconciler) buildImageSaverJob(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) *batchv1.Job {
	// Extract container ID without the runtime prefix (e.g., "containerd://")
	containerID := imageSaver.Status.ContainerID
	if len(containerID) > 12 && containerID[:12] == "containerd://" {
		containerID = containerID[12:]
	}

	// Build the full image name
	fullImageName := fmt.Sprintf("%s:%s", imageSaver.Spec.Repository, imageSaver.Spec.Tag)

	// Prepare environment variables
	env := []corev1.EnvVar{
		{Name: "CONTAINER_ID", Value: containerID},
		{Name: "IMAGE_NAME", Value: fullImageName},
		{Name: "CRI_SOCKET", Value: utils.GetCRISocket()},
	}

	// Add registry secret if specified
	var imagePullSecrets []corev1.LocalObjectReference
	if imageSaver.Spec.RegistrySecret != "" {
		imagePullSecrets = append(imagePullSecrets, corev1.LocalObjectReference{
			Name: imageSaver.Spec.RegistrySecret,
		})
		env = append(env, corev1.EnvVar{
			Name:  "REGISTRY_SECRET",
			Value: imageSaver.Spec.RegistrySecret,
		})
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: imageSaver.Namespace,
			Labels: map[string]string{
				"app":                           "notebook-image-saver",
				"notebook-image-saver.name":     imageSaver.Name,
				"notebook-image-saver.notebook": imageSaver.Spec.NotebookName,
			},
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					RestartPolicy:      corev1.RestartPolicyNever,
					NodeName:           imageSaver.Status.NodeName, // Schedule on the same node as notebook
					ImagePullSecrets:   imagePullSecrets,
					ServiceAccountName: "notebook-image-saver", // TODO: Create this service account
					Containers: []corev1.Container{
						{
							Name:  "image-saver",
							Image: utils.GetImageSaverImage(),
							Env:   env,
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      CRISocketVolumeName,
									MountPath: utils.GetCRISocket(),
								},
								{
									Name:      NerdctlConfigVolumeName,
									MountPath: NerdctlVolumePath,
								},
								{
									Name:      RegistryCAVolumeName,
									MountPath: "/etc/ssl/certs/registry-ca.crt",
								},
							},
							SecurityContext: &corev1.SecurityContext{
								Privileged: &[]bool{true}[0], // Need privileged access to containerd socket
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: CRISocketVolumeName,
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: utils.GetCRISocket(),
									Type: &[]corev1.HostPathType{corev1.HostPathSocket}[0],
								},
							},
						},
						{
							Name: NerdctlConfigVolumeName,
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: NerdctlVolumePath,
									Type: &[]corev1.HostPathType{corev1.HostPathFile}[0],
								},
							},
						},
						{
							Name: RegistryCAVolumeName,
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									SecretName: RegistryCAVolumeName,
								},
							},
						},
					},
				},
			},
		},
	}

	return job
}

// monitorImageSaverJob monitors the image saver job and updates status accordingly
func (r *NotebookImageSaverReconciler) monitorImageSaverJob(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Get the job
	job := &batchv1.Job{}
	err := r.Get(ctx, types.NamespacedName{
		Name:      imageSaver.Status.JobName,
		Namespace: imageSaver.Namespace,
	}, job)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			logger.Info("Job not found, marking as failed")
			imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
			imageSaver.Status.Message = "Image saver job not found"
			imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}
			r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionFalse, "JobNotFound", "Image saver job not found")
			return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
		}
		logger.Error(err, "Failed to get job")
		return ctrl.Result{}, err
	}

	// Check job status
	if job.Status.Succeeded > 0 {
		// Job succeeded
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseSucceeded
		imageSaver.Status.Message = "Image saved successfully"
		imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionTrue, "ImageSaved", "Image saved successfully")
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionTrue, "ImageSaved", "Notebook image saved successfully")
		r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "ImageSaved", "Notebook image saved successfully")
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	}

	if job.Status.Failed > 0 {
		// Job failed
		imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
		imageSaver.Status.Message = "Image saving failed"
		imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}
		r.setCondition(imageSaver, systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionFalse, "ImageSaveFailed", "Notebook image saving failed")
		r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "ImageSaveFailed", "Notebook image saving failed")
		return ctrl.Result{}, r.Status().Update(ctx, imageSaver)
	}

	// Job is still running, requeue after some time
	logger.Info("Job is still running, requeuing")
	return ctrl.Result{RequeueAfter: time.Minute}, nil
}

// setCondition sets a condition in the NotebookImageSaver status
func (r *NotebookImageSaverReconciler) setCondition(imageSaver *systemv1alpha1.NotebookImageSaver, conditionType systemv1alpha1.NotebookImageSaverConditionType, status corev1.ConditionStatus, reason, message string) {
	now := metav1.Now()

	// Find existing condition
	for i, condition := range imageSaver.Status.Conditions {
		if condition.Type == conditionType {
			// Update existing condition
			if condition.Status != status {
				condition.LastTransitionTime = now
			}
			condition.Status = status
			condition.Reason = reason
			condition.Message = message
			condition.LastProbeTime = now
			imageSaver.Status.Conditions[i] = condition
			return
		}
	}

	// Add new condition
	newCondition := systemv1alpha1.NotebookImageSaverCondition{
		Type:               conditionType,
		Status:             status,
		LastTransitionTime: now,
		LastProbeTime:      now,
		Reason:             reason,
		Message:            message,
	}
	imageSaver.Status.Conditions = append(imageSaver.Status.Conditions, newCondition)
}

// getCondition gets a condition from the NotebookImageSaver status
func (r *NotebookImageSaverReconciler) getCondition(imageSaver *systemv1alpha1.NotebookImageSaver, conditionType systemv1alpha1.NotebookImageSaverConditionType) *systemv1alpha1.NotebookImageSaverCondition {
	for _, condition := range imageSaver.Status.Conditions {
		if condition.Type == conditionType {
			return &condition
		}
	}
	return nil
}

// isConditionTrue checks if a condition is true
func (r *NotebookImageSaverReconciler) isConditionTrue(imageSaver *systemv1alpha1.NotebookImageSaver, conditionType systemv1alpha1.NotebookImageSaverConditionType) bool {
	condition := r.getCondition(imageSaver, conditionType)
	return condition != nil && condition.Status == corev1.ConditionTrue
}

// SetupWithManager sets up the controller with the Manager.
func (r *NotebookImageSaverReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.NotebookImageSaver{}).
		Owns(&batchv1.Job{}).
		Complete(r)
}
