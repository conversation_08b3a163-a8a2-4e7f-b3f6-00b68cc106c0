/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	"github.com/go-logr/logr"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"
)

// NotebookImageSaver related constants
const (
	// ImageSaverJobPrefix is the prefix for image saver job names
	ImageSaverJobPrefix  = "isaver"
	CRISocketVolumeName  = "cri-socket"
	RegistryCAVolumeName = "registry-ca"
)

// NotebookImageSaverReconciler reconciles a NotebookImageSaver object
type NotebookImageSaverReconciler struct {
	client.Client
	Logger        logr.Logger
	Scheme        *runtime.Scheme
	EventRecorder record.EventRecorder
}

// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers/finalizers,verbs=update
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks,verbs=get;list;watch
// +kubebuilder:rbac:groups=batch,resources=jobs,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
// +kubebuilder:rbac:groups="",resources=events,verbs=create;patch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *NotebookImageSaverReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	r.Logger = log.FromContext(ctx, "image-saver", req.Name)
	_ = log.IntoContext(ctx, r.Logger)
	r.Logger.Info("reconciling image-saver")

	// Fetch the NotebookImageSaver instance
	imageSaver := &systemv1alpha1.NotebookImageSaver{}
	if err := r.Get(ctx, req.NamespacedName, imageSaver); err != nil {
		if k8serrors.IsNotFound(err) {
			r.Logger.Info("NotebookImageSaver resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		r.Logger.Error(err, "Failed to get NotebookImageSaver")
		return ctrl.Result{}, err
	}

	// Process the image saver based on its current phase
	return r.processImageSaver(ctx, imageSaver)
}

func (r *NotebookImageSaverReconciler) updateStatus(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	r.Logger.Info("updateNoteBookImageSaverStatus", "saver", imageSaver.Name, "status", imageSaver.Status.Phase)
	if e := r.Status().Update(ctx, imageSaver); e != nil && k8serrors.IsConflict(e) {
		return reconcile.Result{Requeue: true}, nil
	}
	return ctrl.Result{}, nil
}

// processImageSaver processes the image saver based on its current phase
func (r *NotebookImageSaverReconciler) processImageSaver(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	switch imageSaver.Status.Phase {
	case "":
		// Initialize the image saver
		return r.initializeImageSaver(ctx, imageSaver)
	case systemv1alpha1.NotebookImageSaverPhasePending:
		// Create the job
		return r.createImageSaverJob(ctx, imageSaver)
	case systemv1alpha1.NotebookImageSaverPhaseRunning:
		// Monitor the job
		return r.monitorImageSaverJob(ctx, imageSaver)
	case systemv1alpha1.NotebookImageSaverPhaseSucceeded, systemv1alpha1.NotebookImageSaverPhaseFailed:
		// Job completed, no further action needed
		r.Logger.Info("Image saver completed", "phase", imageSaver.Status.Phase)
		return ctrl.Result{}, nil
	default:
		r.Logger.Info("Unknown phase", "phase", imageSaver.Status.Phase)
		return ctrl.Result{}, nil
	}
}

// initializeImageSaver initializes the image saver and gathers necessary information
func (r *NotebookImageSaverReconciler) initializeImageSaver(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	notebook := &systemv1alpha1.Notebook{}
	err := r.Get(ctx, types.NamespacedName{
		Name:      imageSaver.Spec.NotebookName,
		Namespace: imageSaver.Namespace,
	}, notebook)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			message := fmt.Sprintf("Notebook %s not found", imageSaver.Spec.NotebookName)
			SetFailedState(imageSaver, "NotebookNotFound", message)
			return r.updateStatus(ctx, imageSaver)
		}
		r.Logger.Error(err, "Failed to get notebook")
		return ctrl.Result{}, err
	}

	if err := controllerutil.SetControllerReference(notebook, imageSaver, r.Scheme); err != nil {
		r.Logger.Error(err, "Failed to set controller reference for ImageSaver")
		return ctrl.Result{}, err
	}

	// Check if notebook is running
	if notebook.Status.NotebookState != constants.NotebookStateRunning {
		SetFailedState(imageSaver, "NotebookNotRunning", "Notebook is not in running state")
		return r.updateStatus(ctx, imageSaver)
	}

	// Get the pod
	pod := &corev1.Pod{}
	err = r.Get(ctx, types.NamespacedName{
		Name:      notebook.Name + "-0", // StatefulSet pod naming convention
		Namespace: imageSaver.Namespace,
	}, pod)
	if err != nil {
		r.Logger.Error(err, "Failed to get notebook pod")
		SetFailedState(imageSaver, "PodNotFound", "Failed to get notebook pod")
		return r.updateStatus(ctx, imageSaver)
	}

	// Get container name
	containerName := constants.NotebookContainerName

	// Find the container and get its ID
	var containerID string
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Name == containerName {
			containerID = containerStatus.ContainerID
			break
		}
	}

	if containerID == "" {
		message := fmt.Sprintf("Container %s not found in pod", containerName)
		SetFailedState(imageSaver, "ContainerNotFound", message)
		return r.updateStatus(ctx, imageSaver)
	}

	// Update status with gathered information
	SetContainerInfo(imageSaver, pod.Spec.NodeName, containerID)
	SetInitializingState(imageSaver, "Initializing image saver")

	r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "Initialized", "Image saver initialized")
	return r.updateStatus(ctx, imageSaver)
}

// createImageSaverJob creates a job to save the notebook image
func (r *NotebookImageSaverReconciler) createImageSaverJob(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	jobName := fmt.Sprintf("%s-%s", ImageSaverJobPrefix, imageSaver.Name)

	// Check if job already exists
	existingJob := &batchv1.Job{}
	err := r.Get(ctx, types.NamespacedName{Name: jobName, Namespace: imageSaver.Namespace}, existingJob)
	if err == nil {
		// Job already exists, update status and move to running phase
		SetJobExistsState(imageSaver, jobName)
		return r.updateStatus(ctx, imageSaver)
	} else if !k8serrors.IsNotFound(err) {
		r.Logger.Error(err, "Failed to get existing job")
		return ctrl.Result{}, err
	}

	// Create the job
	job := r.buildImageSaverJob(imageSaver, jobName)

	// Set owner reference
	if err := controllerutil.SetControllerReference(imageSaver, job, r.Scheme); err != nil {
		r.Logger.Error(err, "Failed to set controller reference")
		return ctrl.Result{}, err
	}

	if err := r.Create(ctx, job); err != nil {
		r.Logger.Error(err, "Failed to create image saver job")
		SetJobCreationFailedState(imageSaver, err)
		r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "JobCreationFailed", err.Error())
		return r.updateStatus(ctx, imageSaver)
	}

	// Update status
	SetJobCreatedState(imageSaver, jobName)
	r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "JobCreated", "Image saver job created")
	return r.updateStatus(ctx, imageSaver)
}

// buildImageSaverJob builds the job specification for saving the notebook image
func (r *NotebookImageSaverReconciler) buildImageSaverJob(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) *batchv1.Job {
	// Extract container ID without the runtime prefix (e.g., "containerd://")
	containerID := imageSaver.Status.ContainerID

	if len(containerID) > 12 && containerID[:13] == "containerd://" {
		containerID = containerID[13:][:12]
	}

	// Build the full image name
	fullImageName := fmt.Sprintf("%s/%s:%s", utils.GetRegistryAddress(), imageSaver.Spec.Repository, imageSaver.Spec.Tag)

	// Prepare environment variables
	env := []corev1.EnvVar{
		{Name: "CONTAINER_ID", Value: containerID},
		{Name: "IMAGE_NAME", Value: fullImageName},
		{Name: "CRI_SOCKET", Value: utils.GetCRISocket()},
	}

	// Add registry secret if specified
	var imagePullSecrets []corev1.LocalObjectReference
	if imageSaver.Spec.RegistrySecret != "" {
		imagePullSecrets = append(imagePullSecrets, corev1.LocalObjectReference{
			Name: imageSaver.Spec.RegistrySecret,
		})
		env = append(env, corev1.EnvVar{
			Name:  "REGISTRY_SECRET",
			Value: imageSaver.Spec.RegistrySecret,
		})
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: imageSaver.Namespace,
			Labels: map[string]string{
				"app":                           "notebook-image-saver",
				"notebook-image-saver.name":     imageSaver.Name,
				"notebook-image-saver.notebook": imageSaver.Spec.NotebookName,
			},
		},
		Spec: batchv1.JobSpec{
			BackoffLimit: &[]int32{0}[0],
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					RestartPolicy:    corev1.RestartPolicyNever,
					NodeName:         imageSaver.Status.NodeName, // Schedule on the same node as notebook
					ImagePullSecrets: imagePullSecrets,
					Containers: []corev1.Container{
						{
							Name:  "image-saver",
							Image: utils.GetImageSaverImage(),
							Env:   env,
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      CRISocketVolumeName,
									MountPath: utils.GetCRISocket(),
								},
								{
									Name:      RegistryCAVolumeName,
									MountPath: "/etc/ssl/certs",
								},
							},
							SecurityContext: &corev1.SecurityContext{
								Privileged: &[]bool{true}[0], // Need privileged access to containerd socket
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: CRISocketVolumeName,
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: utils.GetCRISocket(),
									Type: &[]corev1.HostPathType{corev1.HostPathSocket}[0],
								},
							},
						},
						{
							Name: RegistryCAVolumeName,
							VolumeSource: corev1.VolumeSource{
								Secret: &corev1.SecretVolumeSource{
									SecretName: RegistryCAVolumeName,
								},
							},
						},
					},
				},
			},
		},
	}

	return job
}

// monitorImageSaverJob monitors the image saver job and updates status accordingly
func (r *NotebookImageSaverReconciler) monitorImageSaverJob(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) {
	// Get the job
	job := &batchv1.Job{}
	err := r.Get(ctx, types.NamespacedName{
		Name:      imageSaver.Status.JobName,
		Namespace: imageSaver.Namespace,
	}, job)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			r.Logger.Info("Job not found, marking as failed")
			SetJobNotFoundState(imageSaver)
			return r.updateStatus(ctx, imageSaver)
		}
		r.Logger.Error(err, "Failed to get job")
		return ctrl.Result{}, err
	}

	// Check job status
	if completed, conditionType := isBatchJobComplete(job); !completed {
		switch conditionType {
		case batchv1.JobComplete:
			// Job succeeded
			SetImageSavedState(imageSaver)
			r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "ImageSaved", "Notebook image saved successfully")
		case batchv1.JobFailed:
			// Job failed
			SetImageSaveFailedState(imageSaver, "Notebook image saving failed")
			r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "ImageSaveFailed", "Notebook image saving failed")
		default:
			r.Logger.Info("unknown job condition")
		}
		return r.updateStatus(ctx, imageSaver)
	}

	// Job is still running, requeue after some time
	r.Logger.Info("Job is still running, requeuing")
	return ctrl.Result{RequeueAfter: time.Minute}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *NotebookImageSaverReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.NotebookImageSaver{}).
		Owns(&batchv1.Job{}).
		Complete(r)
}
