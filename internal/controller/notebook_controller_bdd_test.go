/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"math/rand"
	"time"

	// Third-party imports
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	ctrl "sigs.k8s.io/controller-runtime/pkg/reconcile"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
)

var _ = Describe("Notebook Controller BDD Tests", func() {
	// 定义常量和变量
	const (
		Namespace = "default"
		timeout   = time.Second * 10
		interval  = time.Millisecond * 250
	)

	var (
		ctx               context.Context
		notebook          *systemv1alpha1.Notebook
		notebookName      string
		notebookLookupKey types.NamespacedName
	)

	BeforeEach(func() {
		ctx = context.Background()
		notebookName = "test-notebook-" + time.Now().Format("150405") + "-" + randomString(5)
		notebookLookupKey = types.NamespacedName{Name: notebookName, Namespace: Namespace}

		notebook = &systemv1alpha1.Notebook{
			ObjectMeta: metav1.ObjectMeta{
				Name:      notebookName,
				Namespace: Namespace,
			},
			Spec: systemv1alpha1.NotebookSpec{
				PodTemplate: v1.PodTemplateSpec{
					Spec: v1.PodSpec{
						Containers: []v1.Container{{
							Name:  "busybox",
							Image: "busybox",
						}},
					},
				},
			},
		}
	})

	AfterEach(func() {
		notebooks := &systemv1alpha1.NotebookList{}
		err := k8sClient.List(ctx, notebooks, client.InNamespace(Namespace))
		Expect(err).ToNot(HaveOccurred())

		for i := range notebooks.Items {
			notebook := &notebooks.Items[i]

			if len(notebook.Finalizers) > 0 {
				notebook.Finalizers = nil
				err = k8sClient.Update(ctx, notebook)
				Expect(err).ToNot(HaveOccurred())
			}

			err = k8sClient.Delete(ctx, notebook, client.GracePeriodSeconds(0))
			Expect(err).ToNot(HaveOccurred())

			Eventually(func() bool {
				err := k8sClient.Get(ctx, types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, &systemv1alpha1.Notebook{})
				return k8serrors.IsNotFound(err)
			}, timeout, interval).Should(BeTrue())
		}

		// 确保生成唯一名称
		time.Sleep(1 * time.Second)
	})

	Context("When Notebook resource does not exist", func() {
		It("Should handle resource not found error", func() {
			By("By requesting a non-existent Notebook")
			req := ctrl.Request{
				NamespacedName: types.NamespacedName{
					Name:      "nonexistent-notebook",
					Namespace: Namespace,
				},
			}

			reconciler := &NotebookReconciler{
				Client:        k8sClient,
				Scheme:        k8sClient.Scheme(),
				Log:           logf.Log.WithName("controllers").WithName("notebook-controller"),
				EventRecorder: k8sManager.GetEventRecorderFor("notebook-controller"),
			}

			By("By reconciling the non-existent resource")
			_, err := reconciler.Reconcile(ctx, req)

			By("By verifying the error is handled properly")
			Expect(client.IgnoreNotFound(err)).NotTo(HaveOccurred())
		})
	})

	Context("When validating the notebook controller", func() {
		It("Should create replicas", func() {
			By("By creating a new Notebook")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the Notebook has statefulset and status is Running")
			Eventually(func() (bool, error) {
				sts := &appsv1.StatefulSet{ObjectMeta: metav1.ObjectMeta{
					Name:      notebookName,
					Namespace: Namespace,
				}}
				err := k8sClient.Get(ctx, notebookLookupKey, sts)
				if err != nil {
					return false, err
				}

				if createdNotebook.Status.NotebookState == constants.NotebookStateRunning {
					return true, nil
				}
				return true, nil
			}, timeout, interval).Should(BeTrue())
		})

		It("Should not update container image when notebook is running", func() {
			By("By creating a new Notebook with specific image version")
			notebook.Spec.PodTemplate.Spec.Containers[0].Image = "busybox:1.0"
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the StatefulSet has correct initial image and is running")
			stsLookupKey := types.NamespacedName{Name: notebookName, Namespace: Namespace}
			createdSts := &appsv1.StatefulSet{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, stsLookupKey, createdSts)
				if err != nil {
					return false
				}
				return len(createdSts.Spec.Template.Spec.Containers) > 0 &&
					createdSts.Spec.Template.Spec.Containers[0].Image == "busybox:1.0" &&
					createdSts.Spec.Replicas != nil &&
					*createdSts.Spec.Replicas == 1
			}, timeout, interval).Should(BeTrue())

			By("By updating the Notebook's image when it's in running state")
			updatedNotebook := &systemv1alpha1.Notebook{}
			Expect(k8sClient.Get(ctx, notebookLookupKey, updatedNotebook)).Should(Succeed())

			if updatedNotebook.Annotations != nil {
				delete(updatedNotebook.Annotations, constants.STOP_ANNOTATION)
			}

			updatedNotebook.Spec.PodTemplate.Spec.Containers[0].Image = "busybox:1.1"
			Expect(k8sClient.Update(ctx, updatedNotebook)).Should(Succeed())

			By("By checking that the StatefulSet is NOT updated with new image when in running state")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, stsLookupKey, createdSts)
				if err != nil {
					return false
				}
				// Image should still be the original one, not the updated one
				return len(createdSts.Spec.Template.Spec.Containers) > 0 &&
					createdSts.Spec.Template.Spec.Containers[0].Image == "busybox:1.0" &&
					createdSts.Spec.Replicas != nil &&
					*createdSts.Spec.Replicas == 1
			}, timeout, interval).Should(BeTrue())
		})

		It("Should stop notebook when stop annotation key cetccloud.ai/stop-nb is added", func() {
			By("By creating a new Notebook")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the StatefulSet has 1 replica initially")
			stsLookupKey := types.NamespacedName{Name: notebookName, Namespace: Namespace}
			createdSts := &appsv1.StatefulSet{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, stsLookupKey, createdSts)
				if err != nil {
					return false
				}
				return createdSts.Spec.Replicas != nil && *createdSts.Spec.Replicas == 1
			}, timeout, interval).Should(BeTrue())

			By("By adding stop annotation to the Notebook")
			updatedNotebook := &systemv1alpha1.Notebook{}
			Expect(k8sClient.Get(ctx, notebookLookupKey, updatedNotebook)).Should(Succeed())

			if updatedNotebook.Annotations == nil {
				updatedNotebook.Annotations = make(map[string]string)
			}
			currentTime := time.Now().UTC().Format(time.RFC3339)
			updatedNotebook.Annotations[constants.STOP_ANNOTATION] = currentTime

			Expect(k8sClient.Update(ctx, updatedNotebook)).Should(Succeed())

			By("By checking that the StatefulSet replicas is set to 0 and Notebook status is Stopped")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, stsLookupKey, createdSts)
				if err != nil {
					return false
				}
				if createdNotebook.Status.NotebookState == constants.NotebookStateStopped {
					return true
				}
				return createdSts.Spec.Replicas != nil && *createdSts.Spec.Replicas == 0
			}, timeout, interval).Should(BeTrue())

			By("By updating the Notebook's image when restarting")
			restartedNotebook := &systemv1alpha1.Notebook{}
			Expect(k8sClient.Get(ctx, notebookLookupKey, restartedNotebook)).Should(Succeed())

			// Remove stop annotation
			delete(restartedNotebook.Annotations, constants.STOP_ANNOTATION)

			// Update image
			restartedNotebook.Spec.PodTemplate.Spec.Containers[0].Image = "busybox:1.1"
			Expect(k8sClient.Update(ctx, restartedNotebook)).Should(Succeed())

			By("By checking that the Notebook status returns to Running and image is updated")
			Eventually(func() bool {
				// Verify StatefulSet has been updated with new image and replicas=1
				err := k8sClient.Get(ctx, stsLookupKey, createdSts)
				if err != nil {
					return false
				}

				// Verify Notebook state is back to Running
				if err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook); err != nil {
					return false
				}

				imageUpdated := len(createdSts.Spec.Template.Spec.Containers) > 0 &&
					createdSts.Spec.Template.Spec.Containers[0].Image == "busybox:1.1"

				replicasRestored := createdSts.Spec.Replicas != nil && *createdSts.Spec.Replicas == 1

				stateRunning := createdNotebook.Status.NotebookState == constants.NotebookStateRunning

				return imageUpdated && replicasRestored && stateRunning
			}, timeout, interval).Should(BeTrue())
		})

		It("Should handle configuration changes appropriately", func() {
			By("By creating a valid Notebook first")
			// 使用有效配置创建
			notebook.Spec.PodTemplate.Spec.Containers[0].Resources = v1.ResourceRequirements{
				Limits: v1.ResourceList{
					v1.ResourceCPU:    resource.MustParse("100m"),
					v1.ResourceMemory: resource.MustParse("128Mi"),
				},
			}

			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			// 等待 notebook 创建成功
			stsLookupKey := types.NamespacedName{Name: notebookName, Namespace: Namespace}
			Eventually(func() bool {
				sts := &appsv1.StatefulSet{}
				err := k8sClient.Get(ctx, stsLookupKey, sts)
				return err == nil
			}, timeout, interval).Should(BeTrue())
		})

		It("Should correctly setup storage initializer when StorageURI is provided", func() {
			By("By creating a Notebook with StorageURI")
			notebook = &systemv1alpha1.Notebook{
				ObjectMeta: metav1.ObjectMeta{
					Name:      notebookName,
					Namespace: Namespace,
				},
				Spec: systemv1alpha1.NotebookSpec{
					ModelURIS: []string{"cc://root/DeepSeek-R1-Distill-Qwen-1.5B"},
					PodTemplate: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers: []v1.Container{{
								Name:  "notebook",
								Image: "busybox",
							}},
						},
					},
				},
			}

			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the StatefulSet has the storage initializer init container")
			stsLookupKey := types.NamespacedName{Name: notebookName, Namespace: Namespace}
			createdSts := &appsv1.StatefulSet{}

			Eventually(func() bool {
				err := k8sClient.Get(ctx, stsLookupKey, createdSts)
				if err != nil {
					return false
				}

				// 验证StatefulSet中是否有初始化容器
				if len(createdSts.Spec.Template.Spec.InitContainers) == 0 {
					return false
				}

				// 查找名为notebook-storage-initializer的初始化容器
				var initContainer *v1.Container
				for i, container := range createdSts.Spec.Template.Spec.InitContainers {
					if container.Name == "notebook-storage-initializer" {
						initContainer = &createdSts.Spec.Template.Spec.InitContainers[i]
						break
					}
				}

				if initContainer == nil {
					return false
				}

				// 验证容器参数
				if len(initContainer.Args) != 2 ||
					initContainer.Args[0] != "cc://root/DeepSeek-R1-Distill-Qwen-1.5B" ||
					initContainer.Args[1] != "/mnt/models" {
					return false
				}

				// 验证挂载点
				if len(initContainer.VolumeMounts) == 0 {
					return false
				}

				// 验证主容器的挂载点
				var mainContainer *v1.Container
				for i, container := range createdSts.Spec.Template.Spec.Containers {
					if container.Name == "notebook" {
						mainContainer = &createdSts.Spec.Template.Spec.Containers[i]
						break
					}
				}

				if mainContainer == nil || len(mainContainer.VolumeMounts) == 0 {
					return false
				}

				// 验证卷
				var volumeFound bool
				for _, volume := range createdSts.Spec.Template.Spec.Volumes {
					if volume.Name == "notebook-provision-location" {
						volumeFound = true
						break
					}
				}

				return volumeFound
			}, timeout, interval).Should(BeTrue())

			By("By verifying storage initializer container configuration")
			err := k8sClient.Get(ctx, stsLookupKey, createdSts)
			Expect(err).NotTo(HaveOccurred())

			var initContainer *v1.Container
			for i, container := range createdSts.Spec.Template.Spec.InitContainers {
				if container.Name == "notebook-storage-initializer" {
					initContainer = &createdSts.Spec.Template.Spec.InitContainers[i]
					break
				}
			}

			Expect(initContainer).NotTo(BeNil())
			Expect(initContainer.Image).To(ContainSubstring("kserve-storage-initializer"))
			Expect(initContainer.Args).To(Equal([]string{"cc://root/DeepSeek-R1-Distill-Qwen-1.5B", "/mnt/models"}))

			// 验证存储初始化器的资源限制
			cpuLimit := initContainer.Resources.Limits[v1.ResourceCPU]
			memoryLimit := initContainer.Resources.Limits[v1.ResourceMemory]
			Expect(cpuLimit.String()).To(Equal("1"))
			Expect(memoryLimit.String()).To(Equal("1Gi"))

			// 验证主容器有只读挂载点
			var mainContainer *v1.Container
			for i, container := range createdSts.Spec.Template.Spec.Containers {
				if container.Name == "notebook" {
					mainContainer = &createdSts.Spec.Template.Spec.Containers[i]
					break
				}
			}

			Expect(mainContainer).NotTo(BeNil())

			var sharedMount *v1.VolumeMount
			for i, mount := range mainContainer.VolumeMounts {
				if mount.Name == "notebook-provision-location" {
					sharedMount = &mainContainer.VolumeMounts[i]
					break
				}
			}

			Expect(sharedMount).NotTo(BeNil())
			Expect(sharedMount.ReadOnly).To(BeTrue())
			Expect(sharedMount.MountPath).To(Equal("/mnt/models"))
		})

		It("Should allow resource updates only when notebook is stopped", func() {
			By("By creating a basic Notebook")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			By("By checking the Notebook was created successfully")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, &systemv1alpha1.Notebook{})
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By stopping the Notebook")
			updatedNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() error {
				err := k8sClient.Get(ctx, notebookLookupKey, updatedNotebook)
				if err != nil {
					return err
				}

				if updatedNotebook.Annotations == nil {
					updatedNotebook.Annotations = make(map[string]string)
				}
				updatedNotebook.Annotations[constants.STOP_ANNOTATION] = time.Now().UTC().Format(time.RFC3339)
				return k8sClient.Update(ctx, updatedNotebook)
			}, timeout, interval).Should(Succeed())
		})

		It("Should support deleting notebooks", func() {
			By("By creating a notebook")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			By("By verifying it was created")
			Eventually(func() error {
				return k8sClient.Get(ctx, notebookLookupKey, &systemv1alpha1.Notebook{})
			}, timeout, interval).Should(Succeed())

			By("By deleting the notebook")
			deleteNotebook := &systemv1alpha1.Notebook{}
			Expect(k8sClient.Get(ctx, notebookLookupKey, deleteNotebook)).Should(Succeed())
			Expect(k8sClient.Delete(ctx, deleteNotebook)).Should(Succeed())

			// 验证删除成功
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, &systemv1alpha1.Notebook{})
				return k8serrors.IsNotFound(err)
			}, timeout, interval).Should(BeTrue())
		})
	})
})

// 添加辅助函数生成随机字符串
func randomString(n int) string {
	const letterBytes = "abcdefghijklmnopqrstuvwxyz"
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}
	return string(b)
}
