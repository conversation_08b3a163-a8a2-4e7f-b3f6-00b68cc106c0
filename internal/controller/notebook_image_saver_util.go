package controller

import (
	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// NewCondition creates a new NotebookImageSaver condition.
func NewCondition(condType systemv1alpha1.NotebookImageSaverConditionType, status corev1.ConditionStatus, reason, message string) *systemv1alpha1.NotebookImageSaverCondition {
	return &systemv1alpha1.NotebookImageSaverCondition{
		Type:               condType,
		Status:             status,
		LastProbeTime:      metav1.Now(),
		LastTransitionTime: metav1.Now(),
		Reason:             reason,
		Message:            message,
	}
}

// GetCondition returns the condition with the provided type.
func GetCondition(status systemv1alpha1.NotebookImageSaverStatus, condType systemv1alpha1.NotebookImageSaverConditionType) *systemv1alpha1.NotebookImageSaverCondition {
	for i := range status.Conditions {
		c := status.Conditions[i]
		if c.Type == condType {
			return &c
		}
	}
	return nil
}

// SetCondition SetDeploymentCondition updates the deployment to include the provided condition. If the condition that
// we are about to add already exists and has the same status and reason then we are not going to update.
func SetCondition(status *systemv1alpha1.NotebookImageSaverStatus, condition systemv1alpha1.NotebookImageSaverCondition) {
	currentCond := GetCondition(*status, condition.Type)
	if currentCond != nil && currentCond.Status == condition.Status && currentCond.Reason == condition.Reason {
		return
	}
	// Do not update lastTransitionTime if the status of the condition doesn't change.
	if currentCond != nil && currentCond.Status == condition.Status {
		condition.LastTransitionTime = currentCond.LastTransitionTime
	}
	newConditions := filterOutCondition(status.Conditions, condition.Type)
	status.Conditions = append(newConditions, condition)
}

// RemoveCondition RemoveDeploymentCondition removes the deployment condition with the provided type.
func RemoveCondition(status *systemv1alpha1.NotebookImageSaverStatus, condType systemv1alpha1.NotebookImageSaverConditionType) {
	status.Conditions = filterOutCondition(status.Conditions, condType)
}

// filterOutCondition returns a new slice of deployment conditions without conditions with the provided type.
func filterOutCondition(conditions []systemv1alpha1.NotebookImageSaverCondition, condType systemv1alpha1.NotebookImageSaverConditionType) []systemv1alpha1.NotebookImageSaverCondition {
	var newConditions []systemv1alpha1.NotebookImageSaverCondition
	for _, c := range conditions {
		if c.Type == condType {
			continue
		}
		newConditions = append(newConditions, c)
	}
	return newConditions
}
