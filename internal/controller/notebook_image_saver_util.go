/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"time"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// NewCondition creates a new NotebookImageSaver condition.
func NewCondition(condType systemv1alpha1.NotebookImageSaverConditionType, status corev1.ConditionStatus, reason, message string) *systemv1alpha1.NotebookImageSaverCondition {
	return &systemv1alpha1.NotebookImageSaverCondition{
		Type:               condType,
		Status:             status,
		LastProbeTime:      metav1.Now(),
		LastTransitionTime: metav1.Now(),
		Reason:             reason,
		Message:            message,
	}
}

// GetCondition returns the condition with the provided type.
func GetCondition(status systemv1alpha1.NotebookImageSaverStatus, condType systemv1alpha1.NotebookImageSaverConditionType) *systemv1alpha1.NotebookImageSaverCondition {
	for i := range status.Conditions {
		c := status.Conditions[i]
		if c.Type == condType {
			return &c
		}
	}
	return nil
}

// SetCondition SetDeploymentCondition updates the deployment to include the provided condition. If the condition that
// we are about to add already exists and has the same status and reason then we are not going to update.
func SetCondition(status *systemv1alpha1.NotebookImageSaverStatus, condition systemv1alpha1.NotebookImageSaverCondition) {
	currentCond := GetCondition(*status, condition.Type)
	if currentCond != nil && currentCond.Status == condition.Status && currentCond.Reason == condition.Reason {
		return
	}
	// Do not update lastTransitionTime if the status of the condition doesn't change.
	if currentCond != nil && currentCond.Status == condition.Status {
		condition.LastTransitionTime = currentCond.LastTransitionTime
	}
	newConditions := filterOutCondition(status.Conditions, condition.Type)
	status.Conditions = append(newConditions, condition)
}

// RemoveCondition RemoveDeploymentCondition removes the deployment condition with the provided type.
func RemoveCondition(status *systemv1alpha1.NotebookImageSaverStatus, condType systemv1alpha1.NotebookImageSaverConditionType) {
	status.Conditions = filterOutCondition(status.Conditions, condType)
}

// filterOutCondition returns a new slice of deployment conditions without conditions with the provided type.
func filterOutCondition(conditions []systemv1alpha1.NotebookImageSaverCondition, condType systemv1alpha1.NotebookImageSaverConditionType) []systemv1alpha1.NotebookImageSaverCondition {
	var newConditions []systemv1alpha1.NotebookImageSaverCondition
	for _, c := range conditions {
		if c.Type == condType {
			continue
		}
		newConditions = append(newConditions, c)
	}
	return newConditions
}

// SetInitializingState sets the image saver to initializing state
func SetInitializingState(imageSaver *systemv1alpha1.NotebookImageSaver, message string) {
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhasePending
	imageSaver.Status.Message = message
	imageSaver.Status.StartTime = &metav1.Time{Time: time.Now()}

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "Initializing", message)
	SetCondition(&imageSaver.Status, *condition)
}

// SetFailedState sets the image saver to failed state
func SetFailedState(imageSaver *systemv1alpha1.NotebookImageSaver, reason, message string) {
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
	imageSaver.Status.Message = message
	imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, reason, message)
	SetCondition(&imageSaver.Status, *condition)
}

// SetJobCreatedState sets the image saver to job created state
func SetJobCreatedState(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) {
	imageSaver.Status.JobName = jobName
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseRunning
	imageSaver.Status.Message = "Image saver job created and running"

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionTrue, "JobCreated", "Image saver job created successfully")
	SetCondition(&imageSaver.Status, *condition)
}

// SetJobExistsState sets the image saver to job exists state
func SetJobExistsState(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) {
	imageSaver.Status.JobName = jobName
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseRunning
	imageSaver.Status.Message = "Image saver job is running"

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionTrue, "JobExists", "Image saver job already exists")
	SetCondition(&imageSaver.Status, *condition)
}

// SetJobCreationFailedState sets the image saver to job creation failed state
func SetJobCreationFailedState(imageSaver *systemv1alpha1.NotebookImageSaver, err error) {
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
	imageSaver.Status.Message = "Failed to create image saver job"

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionFalse, "JobCreationFailed", err.Error())
	SetCondition(&imageSaver.Status, *condition)
}

// SetImageSavedState sets the image saver to succeeded state
func SetImageSavedState(imageSaver *systemv1alpha1.NotebookImageSaver) {
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseSucceeded
	imageSaver.Status.Message = "Image saved successfully"
	imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}

	readyCondition := NewCondition(systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionTrue, "ImageSaved", "Image saved successfully")
	SetCondition(&imageSaver.Status, *readyCondition)

	savedCondition := NewCondition(systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionTrue, "ImageSaved", "Notebook image saved successfully")
	SetCondition(&imageSaver.Status, *savedCondition)
}

// SetImageSaveFailedState sets the image saver to failed state due to image save failure
func SetImageSaveFailedState(imageSaver *systemv1alpha1.NotebookImageSaver, reason string) {
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
	imageSaver.Status.Message = reason
	imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionFalse, "ImageSaveFailed", reason)
	SetCondition(&imageSaver.Status, *condition)
}

// SetJobNotFoundState sets the image saver to failed state due to job not found
func SetJobNotFoundState(imageSaver *systemv1alpha1.NotebookImageSaver) {
	imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
	imageSaver.Status.Message = "Image saver job not found"
	imageSaver.Status.CompletionTime = &metav1.Time{Time: time.Now()}

	condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionFalse, "JobNotFound", "Image saver job not found")
	SetCondition(&imageSaver.Status, *condition)
}

// SetContainerInfo sets container information in the status
func SetContainerInfo(imageSaver *systemv1alpha1.NotebookImageSaver, nodeName, containerID string) {
	imageSaver.Status.NodeName = nodeName
	imageSaver.Status.ContainerID = containerID
}

// SetImageDigest sets the image digest in the status
func SetImageDigest(imageSaver *systemv1alpha1.NotebookImageSaver, digest string) {
	imageSaver.Status.ImageDigest = digest
}

// IsConditionTrue checks if a condition is true
func IsConditionTrue(imageSaver *systemv1alpha1.NotebookImageSaver, conditionType systemv1alpha1.NotebookImageSaverConditionType) bool {
	condition := GetCondition(imageSaver.Status, conditionType)
	return condition != nil && condition.Status == corev1.ConditionTrue
}
