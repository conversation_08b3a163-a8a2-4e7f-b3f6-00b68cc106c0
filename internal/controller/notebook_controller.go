/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"time"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	api "cetccloud/cetccloud-operator/internal/api"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"

	// Third-party imports
	"github.com/go-logr/logr"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/record"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
)

const DefaultFSGroup = int64(100)

// NotebookReconciler reconciles a Notebook object
type NotebookReconciler struct {
	client.Client
	Log           logr.Logger
	Scheme        *runtime.Scheme
	ClientConfig  *rest.Config
	EventRecorder record.EventRecorder
}

// name of our custom finalizer
const finalizerName = "cetccloud.ai/notebook.finalizers"

// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks,verbs="*"
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks/status,verbs="*"
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks/finalizers,verbs="*"
// +kubebuilder:rbac:groups=core,resources=events,verbs="*"
// +kubebuilder:rbac:groups=core,resources=services,verbs="*"
// +kubebuilder:rbac:groups=core,resources=pods,verbs="*"
// +kubebuilder:rbac:groups=core,resources=serviceaccounts,verbs="*"
// +kubebuilder:rbac:groups=core,resources=secrets,verbs="*"
// +kubebuilder:rbac:groups=apps,resources=statefulsets,verbs="*"
// +kubebuilder:rbac:groups=networking.k8s.io,resources=ingresses,verbs="*"

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the Notebook object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.20.2/pkg/reconcile
func (n *NotebookReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	_ = log.FromContext(ctx)
	log := n.Log.WithValues("notebook", req.NamespacedName)
	log.Info("Reconciliation loop started")

	nb := &systemv1alpha1.Notebook{}

	if err := n.Get(ctx, req.NamespacedName, nb); err != nil {
		if k8serrors.IsNotFound(err) {
			return ctrl.Result{}, client.IgnoreNotFound(err)
		}
		return ctrl.Result{}, err
	}

	// 检查对象是否正在被删除
	if !nb.ObjectMeta.DeletionTimestamp.IsZero() {
		if utils.Includes(nb.ObjectMeta.Finalizers, finalizerName) {
			n.EventRecorder.Event(nb, corev1.EventTypeNormal, "Terminating", "Terminating notebook")
			nb.Status.NotebookState = constants.NotebookStateTerminating
			if err := n.updateAppStatus(ctx, nb); err != nil {
				return n.errorHandler(err)
			}

			nb.ObjectMeta.Finalizers = utils.RemoveString(nb.ObjectMeta.Finalizers, finalizerName)
			if err := n.Update(ctx, nb); err != nil {
				return ctrl.Result{}, err
			}
		}
		return ctrl.Result{}, nil
	}

	if !utils.Includes(nb.ObjectMeta.Finalizers, finalizerName) {
		nb.ObjectMeta.Finalizers = append(nb.ObjectMeta.Finalizers, finalizerName)
		if err := n.Update(ctx, nb); err != nil {
			return ctrl.Result{}, err
		}
	}

	sts := &appsv1.StatefulSet{}
	if err := n.Get(ctx, types.NamespacedName{Namespace: req.Namespace, Name: nb.Name}, sts); err != nil {
		n.Log.Info("unable to get sts of Notebook",
			"notebook", nb.Name,
			"namespace", req.Namespace,
			"error", err.Error(),
		)
	}

	foundPod := &corev1.Pod{}
	if err := n.Get(ctx, types.NamespacedName{
		Name:      nb.Name + "-0",
		Namespace: nb.Namespace,
	}, foundPod); err != nil && k8serrors.IsNotFound(err) {
		n.Log.Info("No Pods are currently running for Notebook Server",
			"notebook", nb.Name,
			"namespace", nb.Namespace)
	} else if err != nil {
		return ctrl.Result{}, err
	}

	if err := n.updateNotebookStatus(nb, sts, foundPod); err != nil {
		n.Log.Error(err, "unable to update notebook status",
			"notebook", nb.Name,
			"namespace", nb.Namespace)
		return ctrl.Result{}, err
	}

	done, result, err := n.processNotebookPhase(ctx, nb)
	if done {
		return result, err
	}
	return ctrl.Result{}, nil

}

func (n *NotebookReconciler) deleteExternalResources(notebook *systemv1alpha1.Notebook) error {
	n.Log.Info("Deleting external resources", "notebook", notebook.Name)
	// TODO: delete external resource such as pvc ..
	return nil
}

func (n *NotebookReconciler) processNotebookPhase(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
	switch notebook.Status.NotebookState {
	case "":
		// Create Notebook
		result, err := n.processCreatState(ctx, notebook)
		if err != nil {
			return true, result, err
		}
	case constants.NotebookStateCreating:
		done, result, err := n.processCreatingState(ctx, notebook)
		if done {
			return true, result, err
		}
	case constants.NotebookStateRunning:
		done, result, err := n.processRunningState(ctx, notebook)
		if done {
			return true, result, err
		}
		// Check for image updates when notebook is running
		if n.shouldUpdateImage(ctx, notebook) {
			result, err := n.processImageUpdate(ctx, notebook)
			if err != nil {
				return true, result, err
			}
		}
	case constants.NotebookStateUpdating:
		done, result, err := n.processUpdatingState(ctx, notebook)
		if done {
			return true, result, err
		}
	case constants.NotebookStateStopping:
		done, result, err := n.processStoppingState(ctx, notebook)
		if done {
			return true, result, err
		}
	case constants.NotebookStateStopped:
		result, err := n.processStoppedState(ctx, notebook)
		if err != nil {
			return true, result, err
		}

	}

	return false, ctrl.Result{}, nil
}

func (n *NotebookReconciler) processCreatState(ctx context.Context, notebook *systemv1alpha1.Notebook) (ctrl.Result, error) {
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Creating", "Creating notebook")
	if err := n.CreateOrUpdateNotebook(ctx, notebook); err != nil {
		notebook.Status.NotebookState = constants.NotebookStateFailed
		err := n.updateAppStatus(ctx, notebook)
		if err != nil {
			return n.errorHandler(err)
		}
	}
	notebook.Status.NotebookState = constants.NotebookStateCreating
	err := n.updateAppStatus(ctx, notebook)
	if err != nil {
		return n.errorHandler(err)
	}
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Success", constants.CreatingAppMsg)
	return ctrl.Result{}, nil
}

func (n *NotebookReconciler) processCreatingState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
	if n.shouldStopNotebook(notebook) {
		return n.stopNotebook(ctx, notebook)
	}
	if notebook.Status.ReadyReplicas >= 0 {
		notebook.Status.NotebookState = constants.NotebookStateRunning
		err := n.updateAppStatus(ctx, notebook)
		if err != nil {
			return n.handleError(err)
		}
	}
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Running", constants.RunningAppMsg)
	return false, ctrl.Result{}, nil
}

func (n *NotebookReconciler) processRunningState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
	if n.shouldStopNotebook(notebook) {
		return n.stopNotebook(ctx, notebook)
	}
	return false, ctrl.Result{}, nil
}

func (n *NotebookReconciler) processStoppingState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
	if notebook.Status.ReadyReplicas <= 0 {
		notebook.Status.NotebookState = constants.NotebookStateStopped
		err := n.updateAppStatus(ctx, notebook)
		if err != nil {
			return n.handleError(err)
		}
	}
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Running", string(constants.NotebookStateStopped))
	return false, ctrl.Result{}, nil
}

func (n *NotebookReconciler) processStoppedState(ctx context.Context, notebook *systemv1alpha1.Notebook) (ctrl.Result, error) {
	if metav1.HasAnnotation(notebook.ObjectMeta, constants.STOP_ANNOTATION) {
		return ctrl.Result{}, nil
	}
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Restart", "Restart notebook")
	if err := n.CreateOrUpdateNotebook(ctx, notebook); err != nil {
		notebook.Status.NotebookState = constants.NotebookStateFailed
		err := n.updateAppStatus(ctx, notebook)
		if err != nil {
			return n.errorHandler(err)
		}
	}
	notebook.Status.NotebookState = constants.NotebookStateCreating
	err := n.updateAppStatus(ctx, notebook)
	if err != nil {
		return n.errorHandler(err)
	}
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Success", constants.CreatingAppMsg)
	return ctrl.Result{}, nil
}

func (n *NotebookReconciler) processUpdatingState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
	n.Log.Info("Processing updating state", "notebook", notebook.Name, "namespace", notebook.Namespace)

	// Check if the StatefulSet has been updated and pods are ready
	sts := &appsv1.StatefulSet{}
	err := n.Get(ctx, types.NamespacedName{
		Name:      notebook.Name,
		Namespace: notebook.Namespace,
	}, sts)
	if err != nil {
		n.Log.Error(err, "Failed to get StatefulSet during update")
		return n.handleError(err)
	}

	// Check if the StatefulSet is ready with the new image
	if sts.Status.ReadyReplicas >= 1 && sts.Status.UpdatedReplicas >= 1 {
		// Update completed successfully
		notebook.Status.NotebookState = constants.NotebookStateRunning
		if err := n.updateAppStatus(ctx, notebook); err != nil {
			return n.handleError(err)
		}

		// Remove the image update annotation
		if notebook.Annotations != nil {
			delete(notebook.Annotations, constants.IMAGE_UPDATE_ANNOTATION)
			if err := n.Update(ctx, notebook); err != nil {
				n.Log.Error(err, "Failed to remove image update annotation")
			}
		}

		n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "UpdateCompleted", constants.UpdatingAppMsg+" completed")
		n.Log.Info("Image update completed successfully", "notebook", notebook.Name, "namespace", notebook.Namespace)
		return false, ctrl.Result{}, nil
	}

	// Check if update failed
	if sts.Status.Replicas > 0 && sts.Status.ReadyReplicas == 0 {
		// Check if pods are in error state for too long
		pods := &corev1.PodList{}
		err := n.List(ctx, pods, client.InNamespace(notebook.Namespace), client.MatchingLabels{
			constants.LabelNotebookName: notebook.Name,
		})
		if err == nil && len(pods.Items) > 0 {
			pod := pods.Items[0]
			// If pod has been in non-ready state for more than 5 minutes, consider it failed
			if pod.Status.Phase == corev1.PodFailed ||
				(pod.Status.Phase == corev1.PodPending &&
					time.Since(pod.CreationTimestamp.Time) > 5*time.Minute) {
				notebook.Status.NotebookState = constants.NotebookStateFailed
				if err := n.updateAppStatus(ctx, notebook); err != nil {
					return n.handleError(err)
				}

				// Remove the image update annotation on failure
				if notebook.Annotations != nil {
					delete(notebook.Annotations, constants.IMAGE_UPDATE_ANNOTATION)
					if err := n.Update(ctx, notebook); err != nil {
						n.Log.Error(err, "Failed to remove image update annotation on failure")
					}
				}

				n.EventRecorder.Event(notebook, corev1.EventTypeWarning, "UpdateFailed", "Image update failed")
				return false, ctrl.Result{}, nil
			}
		}
	}

	// Still updating, requeue
	n.Log.Info("Image update in progress", "notebook", notebook.Name,
		"readyReplicas", sts.Status.ReadyReplicas,
		"updatedReplicas", sts.Status.UpdatedReplicas)
	return false, ctrl.Result{RequeueAfter: time.Second * 30}, nil
}

func (n *NotebookReconciler) shouldStopNotebook(notebook *systemv1alpha1.Notebook) bool {
	return metav1.HasAnnotation(notebook.ObjectMeta, constants.STOP_ANNOTATION)
}

// SetupWithManager sets up the controller with the Manager.
func (n *NotebookReconciler) SetupWithManager(mgr ctrl.Manager) error {
	n.ClientConfig = mgr.GetConfig()
	api.SetupAllBackends(n.Client, n.Scheme)

	if err := mgr.GetFieldIndexer().IndexField(context.Background(), &corev1.Pod{}, constants.PodIndexField, func(object client.Object) []string {
		pod := object.(*corev1.Pod)
		if appName, found := pod.Labels[constants.LabelNotebookName]; found {
			return []string{appName}
		}
		return nil
	}); err != nil {
		return err
	}
	return ctrl.NewControllerManagedBy(mgr).
		For(&systemv1alpha1.Notebook{}).
		Owns(&appsv1.StatefulSet{}).
		Owns(&corev1.Service{}).
		Watches(&corev1.Pod{},
			handler.EnqueueRequestsFromMapFunc(n.findPodsForNoteBook),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Complete(n)

}

func (n *NotebookReconciler) updateAppStatus(ctx context.Context, notebook *systemv1alpha1.Notebook) error {
	n.Log.Info("updateNoteBookStatus", "app", notebook.Name, "status", notebook.Status.NotebookState)
	if e := n.Status().Update(ctx, notebook); e != nil {
		return e
	}
	return nil
}

func (n *NotebookReconciler) CreateOrUpdateNotebook(ctx context.Context, notebook *systemv1alpha1.Notebook) error {
	backends := getAllBackends(notebook)
	for _, backend := range backends {
		err := backend.Inject(notebook)
		if err != nil {
			n.Log.Error(err, "Inject failed")
		}
	}

	for _, backend := range backends {
		err := backend.Configure(notebook)
		if err != nil {
			n.Log.Error(err, "Configure failed")
		}
	}

	return nil
}

func (r *NotebookReconciler) StopApp(ctx context.Context, notebook *systemv1alpha1.Notebook) error {

	backends := getAllBackends(notebook)
	for _, backend := range backends {
		r.Log.Info("Cleaning", "backend", backend.Name(), "is", notebook.Name)
		err := backend.Clean(notebook)
		if err != nil {
			r.Log.Error(err, "Clean failed")
			// return err
		}
	}
	return nil
}

func getAllBackends(_ *systemv1alpha1.Notebook) []api.BackendInterface {
	return []api.BackendInterface{
		api.GetServiceBackend(),
		api.GetDeployBackend(),
		api.GetGatewayBackend(),
	}

}

func (n *NotebookReconciler) errorHandler(err error) (ctrl.Result, error) {
	if k8serrors.IsConflict(err) {
		return reconcile.Result{Requeue: true}, nil
	}
	return ctrl.Result{}, err
}

// shouldUpdateImage checks if the notebook image needs to be updated
func (n *NotebookReconciler) shouldUpdateImage(ctx context.Context, notebook *systemv1alpha1.Notebook) bool {
	// Get the current StatefulSet
	sts := &appsv1.StatefulSet{}
	err := n.Get(ctx, types.NamespacedName{
		Name:      notebook.Name,
		Namespace: notebook.Namespace,
	}, sts)
	if err != nil {
		n.Log.Error(err, "Failed to get StatefulSet for image comparison")
		return false
	}

	// Get the desired image from notebook spec
	desiredImage := n.getDesiredImage(notebook)
	if desiredImage == "" {
		return false
	}

	// Get the current image from StatefulSet
	currentImage := n.getCurrentImage(sts)
	if currentImage == "" {
		return false
	}

	// Compare images
	return desiredImage != currentImage
}

// getDesiredImage extracts the desired image from notebook spec
func (n *NotebookReconciler) getDesiredImage(notebook *systemv1alpha1.Notebook) string {
	if len(notebook.Spec.PodTemplate.Spec.Containers) == 0 {
		return ""
	}

	// Find the notebook container
	for _, container := range notebook.Spec.PodTemplate.Spec.Containers {
		if container.Name == constants.NotebookContainerName {
			return container.Image
		}
	}

	// If no specific notebook container found, use the first container
	return notebook.Spec.PodTemplate.Spec.Containers[0].Image
}

// getCurrentImage extracts the current image from StatefulSet
func (n *NotebookReconciler) getCurrentImage(sts *appsv1.StatefulSet) string {
	if len(sts.Spec.Template.Spec.Containers) == 0 {
		return ""
	}

	// Find the notebook container
	for _, container := range sts.Spec.Template.Spec.Containers {
		if container.Name == constants.NotebookContainerName {
			return container.Image
		}
	}

	// If no specific notebook container found, use the first container
	return sts.Spec.Template.Spec.Containers[0].Image
}

// processImageUpdate handles the image update process
func (n *NotebookReconciler) processImageUpdate(ctx context.Context, notebook *systemv1alpha1.Notebook) (ctrl.Result, error) {
	n.Log.Info("Processing image update", "notebook", notebook.Name, "namespace", notebook.Namespace)

	// Record event for image update
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "ImageUpdating", "Updating notebook image")

	// Add annotation to mark image update in progress
	if notebook.Annotations == nil {
		notebook.Annotations = make(map[string]string)
	}
	notebook.Annotations[constants.IMAGE_UPDATE_ANNOTATION] = time.Now().Format(time.RFC3339)

	// Set notebook state to updating
	notebook.Status.NotebookState = constants.NotebookStateUpdating
	if err := n.updateAppStatus(ctx, notebook); err != nil {
		return n.errorHandler(err)
	}

	// Update the notebook object with the annotation
	if err := n.Update(ctx, notebook); err != nil {
		n.Log.Error(err, "Failed to update notebook with image update annotation")
		return n.errorHandler(err)
	}

	// Use the standard CreateOrUpdateNotebook method to ensure consistency
	// This will trigger the backend to update the StatefulSet properly
	if err := n.CreateOrUpdateNotebook(ctx, notebook); err != nil {
		n.Log.Error(err, "Failed to update notebook resources")
		notebook.Status.NotebookState = constants.NotebookStateFailed
		if updateErr := n.updateAppStatus(ctx, notebook); updateErr != nil {
			n.Log.Error(updateErr, "Failed to update notebook status to failed")
		}
		n.EventRecorder.Event(notebook, corev1.EventTypeWarning, "ImageUpdateFailed", err.Error())
		return n.errorHandler(err)
	}

	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "ImageUpdated", "Notebook image update initiated")
	n.Log.Info("Image update initiated", "notebook", notebook.Name, "namespace", notebook.Namespace)

	return ctrl.Result{RequeueAfter: time.Second * 10}, nil
}

func (n *NotebookReconciler) stopNotebook(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) {
	n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Stopping", constants.StoppingAppMsg)
	err := n.StopApp(ctx, notebook)
	if err != nil {
		return n.handleError(err)
	}

	notebook.Status.NotebookState = constants.NotebookStateStopping
	err = n.updateAppStatus(ctx, notebook)
	if err != nil {
		return n.handleError(err)
	}
	return false, ctrl.Result{}, nil
}

func (n *NotebookReconciler) handleError(err error) (bool, ctrl.Result, error) {
	result, err := n.errorHandler(err)
	return true, result, err
}
