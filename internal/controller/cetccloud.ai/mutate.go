package cetccloudai

import (
	cetccloudaiv1alpha1 "cetccloud/cetccloud-operator/api/cetccloud.ai/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"
	"os"
	"strconv"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func getLabels(modeleval *cetccloudaiv1alpha1.ModelEvaluation) map[string]string {
	existingLabels := modeleval.ObjectMeta.Labels
	if existingLabels == nil {
		existingLabels = make(map[string]string)
	}
	existingLabels["modelevaluation-name"] = modeleval.Name
	existingLabels["job-type"] = "modeleval"
	return existingLabels
}

func setupLabelsAndAnnotations(modeleval *cetccloudaiv1alpha1.ModelEvaluation, meta *metav1.ObjectMeta) {
	if meta.Labels == nil {
		meta.Labels = make(map[string]string)
	}
	if meta.Annotations == nil {
		meta.Annotations = make(map[string]string)
	}

	baseLabels := getLabels(modeleval)
	for k, v := range baseLabels {
		meta.Labels[k] = v
	}

	for k, v := range modeleval.ObjectMeta.Labels {
		meta.Labels[k] = v
	}

	for k, v := range modeleval.ObjectMeta.Annotations {
		meta.Annotations[k] = v
	}
}

// addHeadersEnvVar 添加Headers环境变量到容器中（如果存在）
func addHeadersEnvVar(m *cetccloudaiv1alpha1.ModelEvaluation, container *corev1.Container) {
	if m.Spec.Headers != nil && len(m.Spec.Headers) > 0 {
		// 将Headers转换为字符串格式："key1=value1,key2=value2"
		headersStr := ""
		for key, value := range m.Spec.Headers {
			if headersStr != "" {
				headersStr += ","
			}
			headersStr += key + "=" + value
		}

		if headersStr != "" {
			container.Env = append(container.Env, corev1.EnvVar{
				Name:  constants.ModelEvalHeadersEnv,
				Value: headersStr,
			})
		}
	}
}

// buildJobSpec builds the Job specification
func (r *ModelEvaluationReconciler) buildJobSpec(job *batchv1.Job, m *cetccloudaiv1alpha1.ModelEvaluation) {
	setupLabelsAndAnnotations(m, &job.ObjectMeta)

	workingDir := "/workspace"

	image := m.Spec.Image
	if image == "" {
		image = constants.ModelEvalDefaultImage
	}

	resources := m.Spec.Resources
	if resources.Requests == nil && resources.Limits == nil {
		resources = corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse(constants.ModelEvalDefaultCPU),
				corev1.ResourceMemory: resource.MustParse(constants.ModelEvalDefaultMemory),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse(constants.ModelEvalDefaultCPU),
				corev1.ResourceMemory: resource.MustParse(constants.ModelEvalDefaultMemory),
			},
		}
	}

	webhookUrl := m.Spec.WebhookUrl
	if webhookUrl == "" {
		// Fall back to environment variable
		webhookUrl = os.Getenv(constants.ModelEvalWebhookUrlEnv)
		if webhookUrl == "" {
			// Use default webhook URL
			webhookUrl = constants.ModelEvalDefaultWebhookUrl
		}
	}

	mainContainer := corev1.Container{
		Image:      image,
		Name:       "evaluation",
		WorkingDir: workingDir,
		Env: []corev1.EnvVar{
			{
				Name:  constants.ModelEvalTypeEnv,
				Value: m.Spec.EvalType,
			},
			{
				Name:  constants.ModelEvalModelNameEnv,
				Value: m.Spec.ModelName,
			},
			{
				Name:  constants.ModelEvalApiUrlEnv,
				Value: m.Spec.ApiUrl,
			},
			{
				Name:  constants.ModelEvalApiKeyEnv,
				Value: m.Spec.ApiKey,
			},
			{
				Name:  constants.ModelEvalWebhookUrlEnv,
				Value: webhookUrl,
			},
			{
				Name:  constants.ModelEvalTaskNameEnv,
				Value: m.Name,
			},
			{
				Name:  constants.ModelEvalParallelEnv,
				Value: strconv.Itoa(getParallelValue(m)),
			},
			{
				Name:  constants.ModelEvalNumberEnv,
				Value: strconv.Itoa(getNumberValue(m)),
			},
			{
				Name:  constants.ModelEvalConnectTimeoutEnv,
				Value: strconv.Itoa(getConnectTimeoutValue(m)),
			},
			{
				Name:  constants.ModelEvalReadTimeoutEnv,
				Value: strconv.Itoa(getReadTimeoutValue(m)),
			},
			{
				Name:  constants.ModelEvalTemperatureEnv,
				Value: getTemperatureValue(m),
			},
			{
				Name:  constants.ModelEvalTopPEnv,
				Value: getTopPValue(m),
			},
			{
				Name:  constants.ModelEvalTopKEnv,
				Value: getTopKValue(m),
			},
			{
				Name:  constants.ModelEvalAPIEnv,
				Value: getAPIValue(m),
			},
			{
				Name:  constants.ModelEvalLogEveryNQueryEnv,
				Value: strconv.Itoa(getLogEveryNQueryValue(m)),
			},
		},
		Resources: resources,
	}

	// 添加Headers环境变量（如果存在）
	addHeadersEnvVar(m, &mainContainer)

	// Create init containers for storage initialization only if URIs are provided
	var initContainers []corev1.Container
	if len(m.Spec.ModelURIS) > 0 || len(m.Spec.DataSetURIS) > 0 {
		initContainers = utils.CreateModelEvaluationInitContainers(m.Spec.ModelURIS, m.Spec.DataSetURIS, workingDir)
	}

	if job.Labels == nil {
		job.Labels = make(map[string]string)
	}
	jobLabels := utils.LabelsForJob(job.Name)

	jobSpec := batchv1.JobSpec{
		Template: corev1.PodTemplateSpec{
			ObjectMeta: metav1.ObjectMeta{
				Labels: jobLabels,
			},
			Spec: corev1.PodSpec{
				RestartPolicy:  corev1.RestartPolicyNever,
				InitContainers: initContainers,
				Containers:     []corev1.Container{mainContainer},
			},
		},
		BackoffLimit: getBackoffLimit(m),
		// 不要手动设置 selector 字段，让 Kubernetes 自动生成
	}

	job.Spec = jobSpec
}

// getBackoffLimit returns the backoff limit value from ModelEvaluation or default value if not specified
func getBackoffLimit(m *cetccloudaiv1alpha1.ModelEvaluation) *int32 {
	if m.Spec.BackoffLimit != nil {
		return m.Spec.BackoffLimit
	}
	// Default to 0 if not specified
	v := int32(0)
	return &v
}

func getParallelValue(m *cetccloudaiv1alpha1.ModelEvaluation) int {
	if m.Spec.Parallel > 0 {
		return m.Spec.Parallel
	}
	return 1
}

func getNumberValue(m *cetccloudaiv1alpha1.ModelEvaluation) int {
	if m.Spec.Number > 0 {
		return m.Spec.Number
	}
	return 100
}

func getConnectTimeoutValue(m *cetccloudaiv1alpha1.ModelEvaluation) int {
	if m.Spec.ConnectTimeout > 0 {
		return m.Spec.ConnectTimeout
	}
	return 20
}

func getReadTimeoutValue(m *cetccloudaiv1alpha1.ModelEvaluation) int {
	if m.Spec.ReadTimeout > 0 {
		return m.Spec.ReadTimeout
	}
	return 20
}

func getTemperatureValue(m *cetccloudaiv1alpha1.ModelEvaluation) string {
	if m.Spec.Temperature != "" {
		return m.Spec.Temperature
	}
	return "0.0"
}

func getTopPValue(m *cetccloudaiv1alpha1.ModelEvaluation) string {
	return m.Spec.TopP
}

func getTopKValue(m *cetccloudaiv1alpha1.ModelEvaluation) string {
	return m.Spec.TopK
}

func getAPIValue(m *cetccloudaiv1alpha1.ModelEvaluation) string {
	if m.Spec.API != "" {
		return m.Spec.API
	}
	return "openai"
}

func getLogEveryNQueryValue(m *cetccloudaiv1alpha1.ModelEvaluation) int {
	if m.Spec.LogEveryNQuery > 0 {
		return m.Spec.LogEveryNQuery
	}
	return 10
}
