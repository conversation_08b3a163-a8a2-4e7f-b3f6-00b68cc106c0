/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"fmt"
	"time"

	// Third-party imports
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	ctrl "sigs.k8s.io/controller-runtime/pkg/reconcile"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
)

var _ = Describe("NotebookImageSaver Controller BDD Tests", func() {
	// 定义常量和变量
	const (
		Namespace = "default"
		timeout   = time.Second * 60 // 增加超时时间，镜像保存可能需要更长时间
		interval  = time.Millisecond * 500
	)

	var (
		ctx                 context.Context
		notebook            *systemv1alpha1.Notebook
		imageSaver          *systemv1alpha1.NotebookImageSaver
		notebookName        string
		imageSaverName      string
		notebookLookupKey   types.NamespacedName
		imageSaverLookupKey types.NamespacedName
	)

	BeforeEach(func() {
		ctx = context.Background()
		notebookName = "test-notebook-" + time.Now().Format("150405") + "-" + randomString(5)
		imageSaverName = "test-saver-" + time.Now().Format("150405") + "-" + randomString(5)
		notebookLookupKey = types.NamespacedName{Name: notebookName, Namespace: Namespace}
		imageSaverLookupKey = types.NamespacedName{Name: imageSaverName, Namespace: Namespace}

		// 创建测试用的 Notebook
		notebook = &systemv1alpha1.Notebook{
			ObjectMeta: metav1.ObjectMeta{
				Name:      notebookName,
				Namespace: Namespace,
			},
			Spec: systemv1alpha1.NotebookSpec{
				PodTemplate: v1.PodTemplateSpec{
					Spec: v1.PodSpec{
						Containers: []v1.Container{{
							Name:  constants.NotebookContainerName,
							Image: "jupyter/base-notebook:latest",
							Resources: v1.ResourceRequirements{
								Limits: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("100m"),
									v1.ResourceMemory: resource.MustParse("128Mi"),
								},
							},
						}},
					},
				},
			},
		}

		// 创建测试用的 NotebookImageSaver
		imageSaver = &systemv1alpha1.NotebookImageSaver{
			ObjectMeta: metav1.ObjectMeta{
				Name:      imageSaverName,
				Namespace: Namespace,
			},
			Spec: systemv1alpha1.NotebookImageSaverSpec{
				NotebookName: notebookName,
				Repository:   "test-repo/notebook",
				Tag:          "test-tag",
			},
		}
	})

	AfterEach(func() {
		// 清理 NotebookImageSaver 资源
		imageSavers := &systemv1alpha1.NotebookImageSaverList{}
		err := k8sClient.List(ctx, imageSavers, client.InNamespace(Namespace))
		Expect(err).ToNot(HaveOccurred())

		for i := range imageSavers.Items {
			saver := &imageSavers.Items[i]
			err = k8sClient.Delete(ctx, saver, client.GracePeriodSeconds(0))
			if err != nil && !k8serrors.IsNotFound(err) {
				Expect(err).ToNot(HaveOccurred())
			}
		}

		// 清理 Notebook 资源
		notebooks := &systemv1alpha1.NotebookList{}
		err = k8sClient.List(ctx, notebooks, client.InNamespace(Namespace))
		Expect(err).ToNot(HaveOccurred())

		for i := range notebooks.Items {
			notebook := &notebooks.Items[i]

			if len(notebook.Finalizers) > 0 {
				notebook.Finalizers = nil
				err = k8sClient.Update(ctx, notebook)
				if err != nil && !k8serrors.IsNotFound(err) {
					Expect(err).ToNot(HaveOccurred())
				}
			}

			err = k8sClient.Delete(ctx, notebook, client.GracePeriodSeconds(0))
			if err != nil && !k8serrors.IsNotFound(err) {
				Expect(err).ToNot(HaveOccurred())
			}
		}

		// 清理 Job 资源
		jobs := &batchv1.JobList{}
		err = k8sClient.List(ctx, jobs, client.InNamespace(Namespace))
		Expect(err).ToNot(HaveOccurred())

		for i := range jobs.Items {
			job := &jobs.Items[i]
			if job.Labels != nil && job.Labels["app"] == "notebook-image-saver" {
				err = k8sClient.Delete(ctx, job, client.GracePeriodSeconds(0))
				if err != nil && !k8serrors.IsNotFound(err) {
					Expect(err).ToNot(HaveOccurred())
				}
			}
		}

		// 确保生成唯一名称
		time.Sleep(1 * time.Second)
	})

	Context("When NotebookImageSaver resource does not exist", func() {
		It("Should handle resource not found error", func() {
			By("By requesting a non-existent NotebookImageSaver")
			req := ctrl.Request{
				NamespacedName: types.NamespacedName{
					Name:      "nonexistent-saver",
					Namespace: Namespace,
				},
			}

			reconciler := &NotebookImageSaverReconciler{
				Client:        k8sClient,
				Scheme:        k8sClient.Scheme(),
				Logger:        logf.Log.WithName("controllers").WithName("notebook-image-saver-controller"),
				EventRecorder: k8sManager.GetEventRecorderFor("notebook-image-saver-controller"),
			}

			By("By reconciling the non-existent resource")
			_, err := reconciler.Reconcile(ctx, req)

			By("By verifying the error is handled properly")
			Expect(client.IgnoreNotFound(err)).NotTo(HaveOccurred())
		})
	})

	Context("When validating the notebook image saver controller", func() {
		It("Should fail when referenced notebook does not exist", func() {
			By("By creating a NotebookImageSaver without creating the notebook first")
			Expect(k8sClient.Create(ctx, imageSaver)).To(Succeed())

			createdImageSaver := &systemv1alpha1.NotebookImageSaver{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the NotebookImageSaver status is Failed due to notebook not found")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				return createdImageSaver.Status.Phase == systemv1alpha1.NotebookImageSaverPhaseFailed &&
					createdImageSaver.Status.Message != ""
			}, timeout, interval).Should(BeTrue())

			// 验证失败原因
			Expect(createdImageSaver.Status.Message).To(ContainSubstring("not found"))
		})

		It("Should fail when notebook is not in running state", func() {
			By("By creating a notebook first")
			Expect(k8sClient.Create(ctx, notebook)).To(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By ensuring notebook is not in running state")
			// 确保 notebook 状态不是 Running，设置为 Creating 状态
			createdNotebook.Status.NotebookState = constants.NotebookStateCreating
			createdNotebook.Status.Conditions = []systemv1alpha1.NotebookCondition{
				{
					Type:   "Ready",
					Status: "False",
				},
			}
			Expect(k8sClient.Status().Update(ctx, createdNotebook)).To(Succeed())

			By("By creating a NotebookImageSaver when notebook is not running")
			Expect(k8sClient.Create(ctx, imageSaver)).To(Succeed())

			createdImageSaver := &systemv1alpha1.NotebookImageSaver{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the NotebookImageSaver status is Failed due to notebook not running")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				return createdImageSaver.Status.Phase == systemv1alpha1.NotebookImageSaverPhaseFailed
			}, timeout, interval).Should(BeTrue())

			// 验证失败原因
			Expect(createdImageSaver.Status.Message).To(ContainSubstring("not in running state"))
		})

		It("Should successfully create image saver job when notebook is running", func() {
			By("By creating a notebook first")
			Expect(k8sClient.Create(ctx, notebook)).To(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By simulating notebook in running state")
			// 模拟 notebook 处于运行状态，需要设置必需的 conditions 字段
			createdNotebook.Status.NotebookState = constants.NotebookStateRunning
			createdNotebook.Status.Conditions = []systemv1alpha1.NotebookCondition{
				{
					Type:   "Ready",
					Status: "True",
				},
			}
			Expect(k8sClient.Status().Update(ctx, createdNotebook)).To(Succeed())

			By("By creating a mock pod for the notebook")
			// 创建模拟的 Pod
			pod := &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      notebookName + "-0",
					Namespace: Namespace,
					Labels: map[string]string{
						constants.LabelNotebookName: notebookName,
					},
				},
				Spec: v1.PodSpec{
					NodeName: "test-node",
					Containers: []v1.Container{{
						Name:  constants.NotebookContainerName,
						Image: "jupyter/base-notebook:latest",
					}},
				},
				Status: v1.PodStatus{
					Phase: v1.PodRunning,
					ContainerStatuses: []v1.ContainerStatus{{
						Name:        constants.NotebookContainerName,
						ContainerID: "containerd://abc123def456",
						Ready:       true,
						State: v1.ContainerState{
							Running: &v1.ContainerStateRunning{
								StartedAt: metav1.Now(),
							},
						},
					}},
				},
			}
			Expect(k8sClient.Create(ctx, pod)).Should(Succeed())

			By("By creating a NotebookImageSaver")
			Expect(k8sClient.Create(ctx, imageSaver)).Should(Succeed())

			createdImageSaver := &systemv1alpha1.NotebookImageSaver{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By checking that the NotebookImageSaver progresses through phases")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				// 应该从空状态 -> Pending -> Running
				fmt.Printf("image-saver status: %s\n", createdImageSaver.Status.Phase)
				return createdImageSaver.Status.Phase == systemv1alpha1.NotebookImageSaverPhaseRunning ||
					createdImageSaver.Status.Phase == systemv1alpha1.NotebookImageSaverPhasePending
			}, timeout, interval).Should(BeTrue())

			By("By verifying container information is captured")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				return createdImageSaver.Status.NodeName == "test-node" &&
					createdImageSaver.Status.ContainerID != ""
			}, timeout, interval).Should(BeTrue())

			By("By checking that a job is created")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				if createdImageSaver.Status.JobName == "" {
					return false
				}

				// 验证 Job 是否存在
				job := &batchv1.Job{}
				jobKey := types.NamespacedName{
					Name:      createdImageSaver.Status.JobName,
					Namespace: Namespace,
				}
				err = k8sClient.Get(ctx, jobKey, job)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			By("By verifying job configuration")
			job := &batchv1.Job{}
			jobKey := types.NamespacedName{
				Name:      createdImageSaver.Status.JobName,
				Namespace: Namespace,
			}
			Expect(k8sClient.Get(ctx, jobKey, job)).Should(Succeed())

			// 验证 Job 配置
			Expect(job.Labels).To(HaveKeyWithValue("app", "notebook-image-saver"))
			Expect(job.Labels).To(HaveKeyWithValue("notebook-image-saver.name", imageSaverName))
			Expect(job.Labels).To(HaveKeyWithValue("notebook-image-saver.notebook", notebookName))

			// 验证 Pod 模板
			Expect(job.Spec.Template.Spec.NodeName).To(Equal("test-node"))
			Expect(job.Spec.Template.Spec.RestartPolicy).To(Equal(v1.RestartPolicyNever))
			Expect(len(job.Spec.Template.Spec.Containers)).To(Equal(1))

			container := job.Spec.Template.Spec.Containers[0]
			Expect(container.Name).To(Equal("image-saver"))
			Expect(container.SecurityContext.Privileged).To(Equal(&[]bool{true}[0]))

			// 验证环境变量
			envMap := make(map[string]string)
			for _, env := range container.Env {
				envMap[env.Name] = env.Value
			}
			Expect(envMap).To(HaveKey("CONTAINER_ID"))
			Expect(envMap).To(HaveKey("IMAGE_NAME"))
			Expect(envMap).To(HaveKey("CRI_SOCKET"))
			Expect(envMap["IMAGE_NAME"]).To(ContainSubstring("test-repo/notebook:test-tag"))
		})

		It("Should handle job completion and update status accordingly", func() {
			By("By creating a notebook and setting it to running state")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			createdNotebook.Status.NotebookState = constants.NotebookStateRunning
			Expect(k8sClient.Status().Update(ctx, createdNotebook), timeout, interval).Should(Succeed())

			By("By creating a mock pod")
			pod := &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      notebookName + "-0",
					Namespace: Namespace,
				},
				Spec: v1.PodSpec{
					NodeName: "test-node",
					Containers: []v1.Container{{
						Name:  constants.NotebookContainerName,
						Image: "jupyter/base-notebook:latest",
					}},
				},
				Status: v1.PodStatus{
					ContainerStatuses: []v1.ContainerStatus{{
						Name:        constants.NotebookContainerName,
						ContainerID: "containerd://abc123def456",
						Ready:       true,
					}},
				},
			}
			Expect(k8sClient.Create(ctx, pod)).Should(Succeed())

			By("By creating a NotebookImageSaver")
			Expect(k8sClient.Create(ctx, imageSaver)).Should(Succeed())

			createdImageSaver := &systemv1alpha1.NotebookImageSaver{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				return err == nil && createdImageSaver.Status.JobName != ""
			}, timeout, interval).Should(BeTrue())

			By("By simulating job completion")
			job := &batchv1.Job{}
			jobKey := types.NamespacedName{
				Name:      createdImageSaver.Status.JobName,
				Namespace: Namespace,
			}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, jobKey, job)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			// 模拟 Job 成功完成
			job.Status.Conditions = []batchv1.JobCondition{{
				Type:   batchv1.JobComplete,
				Status: v1.ConditionTrue,
			}}
			job.Status.Succeeded = 1
			Expect(k8sClient.Status().Update(ctx, job)).Should(Succeed())

			By("By checking that the NotebookImageSaver status is updated to Succeeded")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				return createdImageSaver.Status.Phase == systemv1alpha1.NotebookImageSaverPhaseSucceeded
			}, timeout, interval).Should(BeTrue())

			// 验证完成时间被设置
			Expect(createdImageSaver.Status.CompletionTime).NotTo(BeNil())
			Expect(createdImageSaver.Status.Message).To(Equal("Image saved successfully"))
		})

		It("Should handle job failure and update status accordingly", func() {
			By("By creating a notebook and setting it to running state")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			createdNotebook.Status.NotebookState = constants.NotebookStateRunning
			Expect(k8sClient.Status().Update(ctx, createdNotebook)).Should(Succeed())

			By("By creating a mock pod")
			pod := &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      notebookName + "-0",
					Namespace: Namespace,
				},
				Spec: v1.PodSpec{
					NodeName: "test-node",
					Containers: []v1.Container{{
						Name:  constants.NotebookContainerName,
						Image: "jupyter/base-notebook:latest",
					}},
				},
				Status: v1.PodStatus{
					ContainerStatuses: []v1.ContainerStatus{{
						Name:        constants.NotebookContainerName,
						ContainerID: "containerd://abc123def456",
						Ready:       true,
					}},
				},
			}
			Expect(k8sClient.Create(ctx, pod)).Should(Succeed())

			By("By creating a NotebookImageSaver")
			Expect(k8sClient.Create(ctx, imageSaver)).Should(Succeed())

			createdImageSaver := &systemv1alpha1.NotebookImageSaver{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				return err == nil && createdImageSaver.Status.JobName != ""
			}, timeout, interval).Should(BeTrue())

			By("By simulating job failure")
			job := &batchv1.Job{}
			jobKey := types.NamespacedName{
				Name:      createdImageSaver.Status.JobName,
				Namespace: Namespace,
			}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, jobKey, job)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			// 模拟 Job 失败
			job.Status.Conditions = []batchv1.JobCondition{{
				Type:   batchv1.JobFailed,
				Status: v1.ConditionTrue,
				Reason: "BackoffLimitExceeded",
			}}
			job.Status.Failed = 1
			Expect(k8sClient.Status().Update(ctx, job)).Should(Succeed())

			By("By checking that the NotebookImageSaver status is updated to Failed")
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				if err != nil {
					return false
				}
				return createdImageSaver.Status.Phase == systemv1alpha1.NotebookImageSaverPhaseFailed
			}, timeout, interval).Should(BeTrue())

			// 验证失败信息
			Expect(createdImageSaver.Status.CompletionTime).NotTo(BeNil())
			Expect(createdImageSaver.Status.Message).To(ContainSubstring("failed"))
		})

		It("Should handle registry secret configuration", func() {
			By("By creating a registry secret")
			secret := &v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "registry-secret",
					Namespace: Namespace,
				},
				Type: v1.SecretTypeDockerConfigJson,
				Data: map[string][]byte{
					".dockerconfigjson": []byte(`{"auths":{"registry.example.com":{"username":"user","password":"pass"}}}`),
				},
			}
			Expect(k8sClient.Create(ctx, secret)).Should(Succeed())

			By("By creating a notebook and setting it to running state")
			Expect(k8sClient.Create(ctx, notebook)).Should(Succeed())

			createdNotebook := &systemv1alpha1.Notebook{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, notebookLookupKey, createdNotebook)
				return err == nil
			}, timeout, interval).Should(BeTrue())

			createdNotebook.Status.NotebookState = constants.NotebookStateRunning
			Expect(k8sClient.Status().Update(ctx, createdNotebook)).Should(Succeed())

			By("By creating a mock pod")
			pod := &v1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Name:      notebookName + "-0",
					Namespace: Namespace,
				},
				Spec: v1.PodSpec{
					NodeName: "test-node",
					Containers: []v1.Container{{
						Name:  constants.NotebookContainerName,
						Image: "jupyter/base-notebook:latest",
					}},
				},
				Status: v1.PodStatus{
					ContainerStatuses: []v1.ContainerStatus{{
						Name:        constants.NotebookContainerName,
						ContainerID: "containerd://abc123def456",
						Ready:       true,
					}},
				},
			}
			Expect(k8sClient.Create(ctx, pod)).Should(Succeed())

			By("By creating a NotebookImageSaver with registry secret")
			imageSaver.Spec.RegistrySecret = "registry-secret"
			Expect(k8sClient.Create(ctx, imageSaver)).Should(Succeed())

			createdImageSaver := &systemv1alpha1.NotebookImageSaver{}
			Eventually(func() bool {
				err := k8sClient.Get(ctx, imageSaverLookupKey, createdImageSaver)
				return err == nil && createdImageSaver.Status.JobName != ""
			}, timeout, interval).Should(BeTrue())

			By("By verifying job has registry secret configuration")
			job := &batchv1.Job{}
			jobKey := types.NamespacedName{
				Name:      createdImageSaver.Status.JobName,
				Namespace: Namespace,
			}
			Expect(k8sClient.Get(ctx, jobKey, job)).Should(Succeed())

			// 验证 ImagePullSecrets
			Expect(len(job.Spec.Template.Spec.ImagePullSecrets)).To(Equal(1))
			Expect(job.Spec.Template.Spec.ImagePullSecrets[0].Name).To(Equal("registry-secret"))

			// 验证环境变量
			container := job.Spec.Template.Spec.Containers[0]
			envMap := make(map[string]string)
			for _, env := range container.Env {
				envMap[env.Name] = env.Value
			}
			Expect(envMap).To(HaveKeyWithValue("REGISTRY_SECRET", "registry-secret"))
		})
	})
})
