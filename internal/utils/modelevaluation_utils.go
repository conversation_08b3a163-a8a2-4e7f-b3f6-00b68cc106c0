/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package utils

import (
	"cetccloud/cetccloud-operator/internal/constants"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

// LabelsForJob returns the labels for selecting the resources
// belonging to the given job name.
func LabelsForJob(jobName string) map[string]string {
	labels := make(map[string]string)
	labels[constants.LabelModelEvalName] = jobName
	labels[constants.LabelModelEvalType] = constants.ModelEvalTypeModelEvaluation
	return labels
}

// CreateStorageInitContainer creates an init container for storage initialization
func CreateStorageInitContainer(name, storageURI, targetPath, storageType string) corev1.Container {
	const (
		CCTokenKey    = "CETCCLOUD_HUB_TOKEN"
		CCEndpointKey = "CETCCLOUD_ENDPOINT"
		CCRepoTypeKey = "CETCCLOUD_REPO_TYPE"
	)

	initContainer := corev1.Container{
		Name:  name,
		Image: GetStorageInitializerImage(),
		Args: []string{
			storageURI,
			targetPath,
		},
		TerminationMessagePolicy: corev1.TerminationMessageFallbackToLogsOnError,
		VolumeMounts: []corev1.VolumeMount{
			{
				Name:      "workspace-storage",
				MountPath: "/workspace",
				ReadOnly:  false,
			},
		},
		Resources: corev1.ResourceRequirements{
			Limits: map[corev1.ResourceName]resource.Quantity{
				corev1.ResourceCPU:    resource.MustParse("1"),
				corev1.ResourceMemory: resource.MustParse("1Gi"),
			},
			Requests: map[corev1.ResourceName]resource.Quantity{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("100Mi"),
			},
		},
		Env: []corev1.EnvVar{},
	}

	// Add environment variable for dataset type
	if storageType == "dataset" {
		initContainer.Env = append(initContainer.Env, corev1.EnvVar{
			Name:  CCRepoTypeKey,
			Value: "dataset",
		})
	}

	return initContainer
}

// ExtractComponentName extracts the component name from a storage URI
func ExtractComponentName(input string) string {
	// 找到最后一个 '/' 后的部分
	lastSlash := strings.LastIndex(input, "/")
	if lastSlash == -1 || lastSlash+1 >= len(input) {
		return ""
	}
	afterSlash := input[lastSlash+1:]

	// 去除冒号后缀
	colonIndex := strings.Index(afterSlash, ":")
	if colonIndex != -1 {
		afterSlash = afterSlash[:colonIndex]
	}

	return afterSlash
}

// CreateModelEvaluationInitContainers creates init containers for both model and dataset storage initialization
func CreateModelEvaluationInitContainers(modelURIs, datasetURIs []string, workingDir string) []corev1.Container {
	var initContainers []corev1.Container

	// Process ModelURIS
	for i, modelURI := range modelURIs {
		initContainer := CreateStorageInitContainer(
			fmt.Sprintf("model-%d-storage-initializer", i),
			modelURI,
			fmt.Sprintf("%s/models/%s", workingDir, ExtractComponentName(modelURI)),
			"model",
		)
		initContainers = append(initContainers, initContainer)
	}

	// Process DataSetURIS
	for i, datasetURI := range datasetURIs {
		initContainer := CreateStorageInitContainer(
			fmt.Sprintf("dataset-%d-storage-initializer", i),
			datasetURI,
			fmt.Sprintf("%s/datasets/%s", workingDir, ExtractComponentName(datasetURI)),
			"dataset",
		)
		initContainers = append(initContainers, initContainer)
	}

	return initContainers
}
