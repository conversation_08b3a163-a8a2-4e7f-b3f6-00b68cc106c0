package utils

import (
	"os"
)

const (
	DefaultGatewayDomain = "cetccloud.com"

	StorageInitializerContainerImage        = "hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer"
	StorageInitializerContainerImageVersion = "latest"

	DefaultSaverImage = "hub.cetccloud.io:5000/jdcloud/notebook-image-saver:latest"

	RegistryAddress = "************:5000"

	DefaultCRISocket = "/run/k3s/containerd/containerd.sock"
)

func GetIngressDomainSuffix() string {
	if value, ok := os.LookupEnv("INGRESS_DOMAIN_SUFFIX"); ok {
		return value
	}

	return DefaultGatewayDomain
}

func GetStorageInitializerImage() string {
	imageRepository := StorageInitializerContainerImage
	imageTag := StorageInitializerContainerImageVersion
	if value, ok := os.LookupEnv("STORAGE_INITIALIZER_IMAGE_REPOSITORY"); ok {
		imageRepository = value
	}
	if value, ok := os.LookupEnv("STORAGE_INITIALIZER_IMAGE_TAG"); ok {
		imageTag = value
	}

	return imageRepository + ":" + imageTag
}

func GetImageSaverImage() string {
	if value, ok := os.LookupEnv("IMAGE_SAVER_IMAGE"); ok {
		return value
	}

	return DefaultSaverImage
}

func GetRegistryAddress() string {
	if value, ok := os.LookupEnv("REGISTRY_ADDRESS"); ok {
		return value
	}

	return RegistryAddress
}

func GetCRISocket() string {
	if value, ok := os.LookupEnv("CRISocket"); ok {
		return value
	}

	return DefaultCRISocket
}
