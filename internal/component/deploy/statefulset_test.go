package deploy

import (
	"context"
	"reflect"
	"strings"
	"testing"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/component/assembly/deploy"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var nb *systemv1alpha1.Notebook
var sts *Statefulset

func init() {
	nb = &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "jupyter-test",
			Namespace: "notebook-test",
		},
		Spec: systemv1alpha1.NotebookSpec{
			ModelURIS: []string{"cc://root/DeepSeek-R1-Distill-Qwen-1.5B"},
			PodTemplate: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{"app": "jupyter-test"},
				},
				Spec: corev1.PodSpec{
					ServiceAccountName: "jupyter-test",
					Containers: []corev1.Container{
						{
							Name:            "notebook",
							Image:           "kubeflownotebookswg/jupyter-scipy:v1.8.0",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									Name:          "http",
									ContainerPort: 8888,
								},
							},
						},
					},
				},
			},
		},
	}
	sts = &Statefulset{}
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))

	// Create ServiceAccount
	serviceAccount := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "jupyter-test",
			Namespace: "notebook-test",
		},
	}

	objs := []client.Object{
		nb,
		serviceAccount,
	}
	sts.Builder = deploy.NewDeployBuilder()
	sts.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)
}

func Test_Configure(t *testing.T) {
	// Configure the statefulset
	err := sts.Configure(nb)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the statefulset was created with the correct configuration
	createdStatefulset := &appsv1.StatefulSet{}
	err = sts.client.Get(context.TODO(), client.ObjectKey{Name: nb.Name, Namespace: nb.Namespace}, createdStatefulset)
	if err != nil {
		t.Errorf("Failed to get statefulset: %v", err)
	}

	// Check replicas
	if createdStatefulset.Spec.Replicas == nil || *createdStatefulset.Spec.Replicas != 1 {
		t.Errorf("Expected replicas to be 1, got %v", createdStatefulset.Spec.Replicas)
	}

	// Check selector
	if createdStatefulset.Spec.Selector.MatchLabels[constants.LabelNotebookName] != nb.Name {
		t.Errorf("Expected selector to have notebook name %s, got %s",
			nb.Name, createdStatefulset.Spec.Selector.MatchLabels[constants.LabelNotebookName])
	}

	// Check pod template
	if createdStatefulset.Spec.Template.Spec.Containers[0].Image != "kubeflownotebookswg/jupyter-scipy:v1.8.0" {
		t.Errorf("Expected container image to be kubeflownotebookswg/jupyter-scipy:v1.8.0, got %s",
			createdStatefulset.Spec.Template.Spec.Containers[0].Image)
	}

	// Check owner reference
	if len(createdStatefulset.OwnerReferences) != 1 {
		t.Errorf("Expected 1 owner reference, got %d", len(createdStatefulset.OwnerReferences))
	} else {
		ownerRef := createdStatefulset.OwnerReferences[0]
		if ownerRef.Name != nb.Name {
			t.Errorf("Expected owner reference name to be %s, got %s", nb.Name, ownerRef.Name)
		}
		if ownerRef.Kind != "Notebook" {
			t.Errorf("Expected owner reference kind to be Notebook, got %s", ownerRef.Kind)
		}
	}
}

func Test_Inject(t *testing.T) {
	// Test the Inject method
	err := sts.Inject(nb)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the notebook's pod template was updated correctly
	podTemplate := nb.Spec.PodTemplate

	// Check service account name
	if podTemplate.Spec.ServiceAccountName != nb.Name {
		t.Errorf("Expected service account name to be %s, got %s", nb.Name, podTemplate.Spec.ServiceAccountName)
	}

	// Check labels
	if podTemplate.Labels[constants.LabelNotebookName] != nb.Name {
		t.Errorf("Expected label %s to be %s, got %s",
			constants.LabelNotebookName, nb.Name, podTemplate.Labels[constants.LabelNotebookName])
	}

	// Check container environment variables
	found := false
	for _, env := range podTemplate.Spec.Containers[0].Env {
		if env.Name == constants.PrefixEnvVar {
			found = true
			expectedValue := "/notebook/" + nb.Name
			if env.Value != expectedValue {
				t.Errorf("Expected env var %s to be %s, got %s",
					constants.PrefixEnvVar, expectedValue, env.Value)
			}
		}
	}
	if !found {
		t.Errorf("Expected to find environment variable %s", constants.PrefixEnvVar)
	}

	// Check termination grace period
	if podTemplate.Spec.TerminationGracePeriodSeconds == nil || *podTemplate.Spec.TerminationGracePeriodSeconds != 60 {
		t.Errorf("Expected termination grace period to be 60, got %v", podTemplate.Spec.TerminationGracePeriodSeconds)
	}
}

func Test_Name(t *testing.T) {
	name := sts.Name()
	if name != "statefulSet" {
		t.Errorf("Expected name to be 'statefulSet', got %s", name)
	}
}

func Test_Clean(t *testing.T) {
	// First create a statefulset to clean
	err := sts.Configure(nb)
	if err != nil {
		t.Errorf("Unexpected error during Configure: %v", err)
	}

	// Now clean it
	err = sts.Clean(nb)
	if err != nil {
		t.Errorf("Unexpected error during Clean: %v", err)
	}

	// Verify the statefulset was updated with replicas=0
	createdStatefulset := &appsv1.StatefulSet{}
	err = sts.client.Get(context.TODO(), client.ObjectKey{Name: nb.Name, Namespace: nb.Namespace}, createdStatefulset)
	if err != nil {
		t.Errorf("Failed to get statefulset: %v", err)
	}

	if createdStatefulset.Spec.Replicas == nil || *createdStatefulset.Spec.Replicas != 0 {
		t.Errorf("Expected replicas to be 0 after Clean, got %v", createdStatefulset.Spec.Replicas)
	}
}

func Test_VolumeClaimTemplates_Default(t *testing.T) {
	// Configure the statefulset with default notebook (no PVC in PodTemplate)
	err := sts.Configure(nb)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the statefulset was created with the correct configuration
	createdStatefulset := &appsv1.StatefulSet{}
	err = sts.client.Get(context.TODO(), client.ObjectKey{Name: nb.Name, Namespace: nb.Namespace}, createdStatefulset)
	if err != nil {
		t.Errorf("Failed to get statefulset: %v", err)
	}

	// Check VolumeClaimTemplates
	if len(createdStatefulset.Spec.VolumeClaimTemplates) != 1 {
		t.Errorf("Expected 1 VolumeClaimTemplate, got %d", len(createdStatefulset.Spec.VolumeClaimTemplates))
	} else {
		pvc := createdStatefulset.Spec.VolumeClaimTemplates[0]
		if pvc.Name != constants.DefaultStorageName {
			t.Errorf("Expected PVC name to be %s, got %s", constants.DefaultStorageName, pvc.Name)
		}
		if len(pvc.Spec.AccessModes) != 1 || pvc.Spec.AccessModes[0] != corev1.ReadWriteOnce {
			t.Errorf("Expected access mode to be ReadWriteOnce, got %v", pvc.Spec.AccessModes)
		}
		if pvc.Spec.Resources.Requests.Storage().String() != constants.DefaultStorageSize {
			t.Errorf("Expected storage size to be %s, got %s", constants.DefaultStorageSize, pvc.Spec.Resources.Requests.Storage().String())
		}
	}

	// Check PersistentVolumeClaimRetentionPolicy
	if createdStatefulset.Spec.PersistentVolumeClaimRetentionPolicy == nil {
		t.Errorf("Expected PersistentVolumeClaimRetentionPolicy to be set")
	} else if createdStatefulset.Spec.PersistentVolumeClaimRetentionPolicy.WhenDeleted != appsv1.DeletePersistentVolumeClaimRetentionPolicyType {
		t.Errorf("Expected WhenDeleted to be Delete, got %s", createdStatefulset.Spec.PersistentVolumeClaimRetentionPolicy.WhenDeleted)
	}
}

func Test_VolumeClaimTemplates_WithExistingPVC(t *testing.T) {
	// Create a notebook with existing PVC in PodTemplate
	notebookWithPVC := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "notebook-with-pvc",
			Namespace: "test-namespace",
		},
		Spec: systemv1alpha1.NotebookSpec{
			PodTemplate: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name: "notebook",
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "existing-pvc",
							VolumeSource: corev1.VolumeSource{
								PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
									ClaimName: "existing-claim",
								},
							},
						},
					},
				},
			},
		},
	}

	// Create a new StatefulSet instance for this test
	scheme := runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))

	objs := []client.Object{
		notebookWithPVC,
	}
	testSts := &Statefulset{}
	testSts.Builder = deploy.NewDeployBuilder()
	testSts.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)

	// Configure the statefulset
	err := testSts.Configure(notebookWithPVC)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the statefulset was created with the correct configuration
	createdStatefulset := &appsv1.StatefulSet{}
	err = testSts.client.Get(context.TODO(), client.ObjectKey{Name: notebookWithPVC.Name, Namespace: notebookWithPVC.Namespace}, createdStatefulset)
	if err != nil {
		t.Errorf("Failed to get statefulset: %v", err)
	}

	// Check that no VolumeClaimTemplates were created since we already have a PVC
	if len(createdStatefulset.Spec.VolumeClaimTemplates) != 0 {
		t.Errorf("Expected 0 VolumeClaimTemplates when PVC exists, got %d", len(createdStatefulset.Spec.VolumeClaimTemplates))
	}
}

func Test_VolumeClaimTemplates_CustomStorageSize(t *testing.T) {
	// Create a notebook with custom storage size annotation
	notebookWithCustomStorage := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "notebook-custom-storage",
			Namespace: "test-namespace",
			Annotations: map[string]string{
				constants.AnnotationStorageSize: "100Gi",
			},
		},
		Spec: systemv1alpha1.NotebookSpec{
			PodTemplate: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name: "notebook",
						},
					},
				},
			},
		},
	}

	// Create a new StatefulSet instance for this test
	scheme := runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))

	objs := []client.Object{
		notebookWithCustomStorage,
	}
	testSts := &Statefulset{}
	testSts.Builder = deploy.NewDeployBuilder()
	testSts.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)

	// Configure the statefulset
	err := testSts.Configure(notebookWithCustomStorage)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the statefulset was created with the correct configuration
	createdStatefulset := &appsv1.StatefulSet{}
	err = testSts.client.Get(context.TODO(), client.ObjectKey{Name: notebookWithCustomStorage.Name, Namespace: notebookWithCustomStorage.Namespace}, createdStatefulset)
	if err != nil {
		t.Errorf("Failed to get statefulset: %v", err)
	}

	// Check VolumeClaimTemplates with custom storage size
	if len(createdStatefulset.Spec.VolumeClaimTemplates) != 1 {
		t.Errorf("Expected 1 VolumeClaimTemplate, got %d", len(createdStatefulset.Spec.VolumeClaimTemplates))
	} else {
		pvc := createdStatefulset.Spec.VolumeClaimTemplates[0]
		expectedSize := "100Gi"
		if pvc.Spec.Resources.Requests.Storage().String() != expectedSize {
			t.Errorf("Expected storage size to be %s, got %s", expectedSize, pvc.Spec.Resources.Requests.Storage().String())
		}
	}
}

func Test_InjectStorageInitializer(t *testing.T) {
	// Create a new StatefulSet instance for this test
	scheme := runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))

	// Create ServiceAccount
	serviceAccount := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "notebook-with-model",
			Namespace: "test-namespace",
		},
	}

	// Create notebook
	nb = &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "notebook-with-model",
			Namespace: "test-namespace",
		},
		Spec: systemv1alpha1.NotebookSpec{
			ModelURIS: []string{"cc://root/DeepSeek-R1-Distill-Qwen-1.5B"},
			PodTemplate: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					ServiceAccountName: "notebook-with-model",
					Containers: []corev1.Container{
						{
							Name: "notebook",
						},
					},
				},
			},
		},
	}

	// Create test client with objects
	objs := []client.Object{
		nb,
		serviceAccount,
	}
	testSts := &Statefulset{}
	testSts.Builder = deploy.NewDeployBuilder()
	testSts.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)

	// Call the function we're testing
	err := testSts.InjectStorageInitializer(nb, "cc://root/DeepSeek-R1-Distill-Qwen-1.5B", "model-0", "models")
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Verify the init container was added correctly
	if len(nb.Spec.PodTemplate.Spec.InitContainers) != 1 {
		t.Errorf("Expected 1 init container, got %d", len(nb.Spec.PodTemplate.Spec.InitContainers))
	}

	// Verify the init container is configured correctly
	initContainer := nb.Spec.PodTemplate.Spec.InitContainers[0]

	// Check name
	if !strings.Contains(initContainer.Name, "storage-initializer") {
		t.Errorf("Expected init container name to contain 'storage-initializer', got %s", initContainer.Name)
	}

	// Check image
	expectedImage := utils.GetStorageInitializerImage()
	if initContainer.Image != expectedImage {
		t.Errorf("Expected init container image to be %s, got %s",
			expectedImage, initContainer.Image)
	}

	// Check arguments
	expectedArgs := []string{
		"cc://root/DeepSeek-R1-Distill-Qwen-1.5B",
		"/home/<USER>/models/DeepSeek-R1-Distill-Qwen-1.5B",
	}
	if !reflect.DeepEqual(initContainer.Args, expectedArgs) {
		t.Errorf("Expected init container args to be %v, got %v",
			expectedArgs, initContainer.Args)
	}

	// Check volume mounts
	if len(initContainer.VolumeMounts) != 1 {
		t.Errorf("Expected 1 volume mount, got %d", len(initContainer.VolumeMounts))
	}

	volumeMount := initContainer.VolumeMounts[0]
	if strings.Contains(volumeMount.Name, "storage-initializer") {
		t.Errorf("Expected volume mount name to be %s, got %s",
			StorageInitializerVolumeName, volumeMount.Name)
	}

	if !strings.Contains(volumeMount.MountPath, "/models") {
		t.Errorf("Expected volume mount path to contain /models, got %s", volumeMount.MountPath)
	}

	// Test that calling it again doesn't add another init container
	err = testSts.InjectStorageInitializer(nb, "cc://root/DeepSeek-R1-Distill-Qwen-1.5B", "model-0", "models")
	if err != nil {
		t.Errorf("Unexpected error on second call: %v", err)
	}

	if len(nb.Spec.PodTemplate.Spec.InitContainers) != 1 {
		t.Errorf("Expected still 1 init container after second call, got %d",
			len(nb.Spec.PodTemplate.Spec.InitContainers))
	}
}

// Test the helper function
func Test_getContainerWithName(t *testing.T) {
	podSpec := &corev1.PodSpec{
		Containers: []corev1.Container{
			{
				Name: "container1",
			},
			{
				Name: "container2",
			},
		},
	}

	// Test finding existing container
	container := getContainerWithName(podSpec, "container2")
	if container == nil {
		t.Error("Expected to find container, got nil")
	}
	if container.Name != "container2" {
		t.Errorf("Expected to find container with name container2, got %s", container.Name)
	}

	// Test not finding container
	container = getContainerWithName(podSpec, "nonexistent")
	if container != nil {
		t.Errorf("Expected nil when container doesn't exist, got %v", container)
	}
}
