package deploy

import (
	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/component/assembly/deploy"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"
	"context"
	"fmt"
	"os"
	"strings"

	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const (
	StorageInitializerSourceUriInternalAnnotationKey = "cetccloud.ai/storage-initializer-source-uri"
	// DefaultModelLocalMountPath is where models will be mounted by the storage-initializer
	DefaultModelLocalMountPath = "/mnt/models"

	StorageInitializerVolumeName = "notebook-provision-location"

	CCTokenKey    = "CETCCLOUD_HUB_TOKEN"
	CCEndpointKey = "CETCCLOUD_ENDPOINT"
	CCRepoTypeKey = "CETCCLOUD_REPO_TYPE"
)

type Statefulset struct {
	client client.Client
	*deploy.Builder
	*runtime.Scheme
}

func (s *Statefulset) Name() string {
	return "statefulSet"
}

const (
	StorageInitializerContainerName = "%s-storage-initializer"
)

func (s *Statefulset) Configure(notebook *systemv1alpha1.Notebook) error {
	namespace := notebook.Namespace
	notebookName := notebook.Name
	annotations := notebook.Annotations

	statefulSet := &v1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:        notebookName,
			Namespace:   namespace,
			Annotations: annotations,
		},
	}

	if _, err := controllerutil.CreateOrUpdate(context.TODO(), s.client, statefulSet, s.MutateFn(statefulSet, notebook)); err != nil {
		return err
	}

	return nil
}

func (s *Statefulset) Inject(notebook *systemv1alpha1.Notebook) error {
	err := s.injectPodTemplate(notebook)
	if err != nil {
		return err
	}
	return nil

}

func (s *Statefulset) Setup(client client.Client, scheme *runtime.Scheme) {
	s.client = client
	s.Scheme = scheme
}

func (s *Statefulset) Clean(n *systemv1alpha1.Notebook) error {
	// TODO: statefulset replicas equal 0
	namespace := n.Namespace
	notebookName := n.Name
	statefulSet := &v1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebookName,
			Namespace: namespace,
		},
	}
	if err := s.client.Get(context.TODO(), client.ObjectKeyFromObject(statefulSet), statefulSet); err != nil {
		return err
	}
	if _, err := controllerutil.CreateOrUpdate(context.TODO(), s.client, statefulSet, func() error {
		statefulSet.Spec.Replicas = ptr.To[int32](0)
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (s *Statefulset) MutateFn(statefulSet *v1.StatefulSet, n *systemv1alpha1.Notebook) controllerutil.MutateFn {
	return func() error {
		s.BuildStatefulSet(statefulSet, n)
		statefulSet.SetOwnerReferences(nil)
		return controllerutil.SetControllerReference(n, statefulSet, s.Scheme)

	}
}

func (s *Statefulset) injectPodTemplate(notebook *systemv1alpha1.Notebook) error {
	if err := s.ensureServiceAccountExists(notebook); err != nil {
		return err
	}

	// 设置 storage-init-container
	modelURIs := notebook.GetModelUris()
	if len(modelURIs) > 0 {
		for i, modelURI := range modelURIs {
			if err := s.InjectStorageInitializer(notebook, modelURI, fmt.Sprintf("model-%d", i), "models"); err != nil {
				return fmt.Errorf("failed to inject model storage initializer: %w", err)
			}
		}
	}

	datasetURIs := notebook.GetDataSetUris()
	if len(datasetURIs) > 0 {
		for i, datasetURI := range datasetURIs {
			if err := s.InjectStorageInitializer(notebook, datasetURI, fmt.Sprintf("dataset-%d", i), "datasets"); err != nil {
				return fmt.Errorf("failed to inject dataset storage initializer: %w", err)
			}
		}
	}

	podTemplate := notebook.Spec.PodTemplate
	podTemplate.Spec.ServiceAccountName = notebook.Name

	// 初始化 labels 和 annotations
	s.initializeMetadata(&podTemplate.ObjectMeta)

	// 设置和复制标签
	s.setupLabelsAndAnnotations(notebook, &podTemplate.ObjectMeta)

	// 设置 Pod 和容器的默认值
	s.setDefaultPodSpec(&podTemplate.Spec, notebook)

	notebook.Spec.PodTemplate = podTemplate

	return nil
}

func (s *Statefulset) initializeMetadata(meta *metav1.ObjectMeta) {
	if meta.Labels == nil {
		meta.Labels = make(map[string]string)
	}
	if meta.Annotations == nil {
		meta.Annotations = make(map[string]string)
	}
}

func (s *Statefulset) setupLabelsAndAnnotations(notebook *systemv1alpha1.Notebook, meta *metav1.ObjectMeta) {
	baseLabels := s.getLabels(notebook)
	for k, v := range baseLabels {
		meta.Labels[k] = v
	}

	for k, v := range notebook.ObjectMeta.Labels {
		meta.Labels[k] = v
	}

	for k, v := range notebook.ObjectMeta.Annotations {
		if !strings.Contains(k, "kubectl") && !strings.Contains(k, "notebook") {
			meta.Annotations[k] = v
		}
	}
}

// setDefaultPodSpec 设置 Pod 规格的默认值
func (s *Statefulset) setDefaultPodSpec(podSpec *corev1.PodSpec, notebook *systemv1alpha1.Notebook) {
	// 设置默认终止宽限期
	if podSpec.TerminationGracePeriodSeconds == nil {
		podSpec.TerminationGracePeriodSeconds = &[]int64{60}[0]
	}

	// 初始化并配置容器
	s.setupDefaultContainers(podSpec, notebook)

	// 设置安全上下文
	s.setupSecurityContext(podSpec)
}

// setupDefaultContainers 设置容器的默认值
func (s *Statefulset) setupDefaultContainers(podSpec *corev1.PodSpec, notebook *systemv1alpha1.Notebook) {
	// 初始化容器列表
	if podSpec.Containers == nil {
		podSpec.Containers = []corev1.Container{
			{
				Name: constants.NotebookContainerName,
			},
		}
	}

	// 配置主容器
	container := &podSpec.Containers[0]

	// 设置工作目录
	if container.WorkingDir == "" {
		container.WorkingDir = constants.DefaultWorkingDir
	}

	// 设置 volumeMount
	needSetVolumeMount := true
	for _, volume := range podSpec.Volumes {
		if volume.PersistentVolumeClaim != nil {
			needSetVolumeMount = false
			break
		}
	}
	if needSetVolumeMount {
		container.VolumeMounts = append(container.VolumeMounts, corev1.VolumeMount{
			Name:      constants.DefaultStorageName,
			MountPath: container.WorkingDir,
		})
	}

	// 设置端口
	if container.Ports == nil {
		container.Ports = []corev1.ContainerPort{
			{
				ContainerPort: constants.DefaultContainerPort,
				Name:          "notebook-port",
				Protocol:      "TCP",
			},
		}
	}

	// 设置环境变量
	s.setupContainerEnvVars(container, notebook)
}

// setupContainerEnvVars 设置容器环境变量
func (s *Statefulset) setupContainerEnvVars(container *corev1.Container, notebook *systemv1alpha1.Notebook) {
	prefix := "/notebook/" + notebook.Name
	if container.Env == nil {
		container.Env = []corev1.EnvVar{}
	}

	for _, envVar := range container.Env {
		if envVar.Name == constants.PrefixEnvVar {
			envVar.Value = prefix
			return
		}
	}

	container.Env = append(container.Env, corev1.EnvVar{
		Name:  constants.PrefixEnvVar,
		Value: prefix,
	},
		corev1.EnvVar{
			Name:  constants.NBHomeEnvVar,
			Value: container.WorkingDir,
		})
}

// setupSecurityContext 设置安全上下文
func (s *Statefulset) setupSecurityContext(podSpec *corev1.PodSpec) {
	if value, exists := os.LookupEnv("ADD_FSGROUP"); !exists || value == "true" {
		if podSpec.SecurityContext == nil {
			fsGroup := constants.DefaultFSGroup
			podSpec.SecurityContext = &corev1.PodSecurityContext{
				FSGroup: &fsGroup,
			}
		}
	}
}

func (s *Statefulset) getLabels(notebook *systemv1alpha1.Notebook) map[string]string {
	existingLabels := notebook.ObjectMeta.Labels
	if existingLabels == nil {
		existingLabels = make(map[string]string)
	}
	existingLabels[constants.LabelNotebookName] = notebook.Name
	existingLabels[constants.LabelStatefulsetName] = notebook.Name
	return existingLabels
}

func (s *Statefulset) ensureServiceAccountExists(notebook *systemv1alpha1.Notebook) error {
	serviceAccountName := notebook.Name
	namespace := notebook.Namespace
	key := client.ObjectKey{Name: serviceAccountName, Namespace: namespace}
	serviceAccount := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceAccountName,
			Namespace: notebook.Namespace,
		},
	}

	if err := controllerutil.SetControllerReference(notebook, serviceAccount, s.Scheme); err != nil {
		return err
	}

	if err := s.client.Get(context.TODO(), key, serviceAccount); err != nil {
		if client.IgnoreNotFound(err) != nil {
			return err
		}
		if _, err := controllerutil.CreateOrUpdate(context.TODO(), s.client, serviceAccount, func() error { return nil }); err != nil {
			return err
		}
	}
	return nil
}

// InjectStorageInitializer 注入存储初始化器
func (s *Statefulset) InjectStorageInitializer(notebook *systemv1alpha1.Notebook, storageURI, name, storageType string) error {
	userContainer := getContainerWithName(&notebook.Spec.PodTemplate.Spec, constants.NotebookContainerName)
	if userContainer == nil {
		return fmt.Errorf("container with name %s not found", constants.NotebookContainerName)
	}

	// Don't inject if InitContainer already injected
	initContainerName := fmt.Sprintf(StorageInitializerContainerName, name)
	for _, container := range notebook.Spec.PodTemplate.Spec.InitContainers {
		for _, args := range container.Args {
			if args == storageURI {
				return nil
			}
		}
	}

	if userContainer.WorkingDir == "" {
		userContainer.WorkingDir = constants.DefaultWorkingDir
	}

	storageInitializerMounts := []corev1.VolumeMount{
		{
			Name:      constants.DefaultStorageName,
			MountPath: fmt.Sprintf("%s/%s", userContainer.WorkingDir, storageType),
			ReadOnly:  false,
			SubPath:   storageType,
		},
	}

	initContainer := &corev1.Container{
		Name:  initContainerName,
		Image: utils.GetStorageInitializerImage(),
		Args: []string{
			storageURI,
			fmt.Sprintf("%s/%s/%s", userContainer.WorkingDir, storageType, extractComponentName(storageURI)),
		},
		TerminationMessagePolicy: corev1.TerminationMessageFallbackToLogsOnError,
		VolumeMounts:             storageInitializerMounts,
		Resources: corev1.ResourceRequirements{
			Limits: map[corev1.ResourceName]resource.Quantity{
				corev1.ResourceCPU:    resource.MustParse("1"),
				corev1.ResourceMemory: resource.MustParse("1Gi"),
			},
			Requests: map[corev1.ResourceName]resource.Quantity{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("100Mi"),
			},
		},
	}

	// Get service account and its secrets
	serviceAccountName := notebook.Spec.PodTemplate.Spec.ServiceAccountName
	if serviceAccountName == "" {
		return fmt.Errorf("service account name is not set")
	}

	serviceAccount := &corev1.ServiceAccount{}
	if err := s.client.Get(context.TODO(), client.ObjectKey{
		Name:      serviceAccountName,
		Namespace: notebook.Namespace,
	}, serviceAccount); err != nil {
		return fmt.Errorf("failed to get service account %s: %w", serviceAccountName, err)
	}

	// Add environment variables from secrets
	for _, secretRef := range serviceAccount.Secrets {
		secret := &corev1.Secret{}
		if err := s.client.Get(context.TODO(), client.ObjectKey{
			Name:      secretRef.Name,
			Namespace: notebook.Namespace,
		}, secret); err != nil {
			continue // Skip if secret not found
		}
		initContainer.Env = append(initContainer.Env, BuildSecretEnvs(secret)...)
	}
	if storageType == "datasets" {
		initContainer.Env = append(initContainer.Env, corev1.EnvVar{
			Name:  CCRepoTypeKey,
			Value: "dataset",
		})
	}
	notebook.Spec.PodTemplate.Spec.InitContainers = append(notebook.Spec.PodTemplate.Spec.InitContainers, *initContainer)
	return nil
}

func getContainerWithName(podSpec *corev1.PodSpec, name string) *corev1.Container {
	for idx, container := range podSpec.Containers {
		if strings.Compare(container.Name, name) == 0 {
			return &podSpec.Containers[idx]
		}
	}
	return nil
}

func BuildSecretEnvs(secret *corev1.Secret) []corev1.EnvVar {
	envs := make([]corev1.EnvVar, 0, 2) // Pre-allocate for two possible env vars

	for _, key := range []string{CCTokenKey, CCEndpointKey} {
		if value, ok := secret.Data[key]; ok {
			envs = append(envs, corev1.EnvVar{
				Name:  key,
				Value: string(value),
			})
		}
	}
	return envs
}
func extractComponentName(input string) string {
	// 找到最后一个 '/' 后的部分
	lastSlash := strings.LastIndex(input, "/")
	if lastSlash == -1 || lastSlash+1 >= len(input) {
		return ""
	}
	afterSlash := input[lastSlash+1:]

	// 去除冒号后缀
	colonIndex := strings.Index(afterSlash, ":")
	if colonIndex != -1 {
		afterSlash = afterSlash[:colonIndex]
	}

	return afterSlash
}
