package gateway

import (
	"context"
	"fmt"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"
	"cetccloud/cetccloud-operator/internal/utils"

	netv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

type IngressNginxBackend struct {
	client client.Client
	scheme *runtime.Scheme
}

func (g *IngressNginxBackend) Name() string {
	return "nginx-ingress"
}

func (g *IngressNginxBackend) Configure(n *systemv1alpha1.Notebook) error {
	// 设置 Nginx Ingress
	ingress := &netv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      n.Name,
			Namespace: n.Namespace,
			Annotations: map[string]string{
				"nginx.ingress.kubernetes.io/cors-allow-credentials": "true",
				"nginx.ingress.kubernetes.io/cors-allow-headers":     "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization",
				"nginx.ingress.kubernetes.io/cors-allow-origin":      "*",
				"nginx.ingress.kubernetes.io/enable-cors":            "true",
			},
		},
	}

	if _, err := controllerutil.CreateOrUpdate(context.TODO(), g.client, ingress, func() error {
		ingress.Spec = netv1.IngressSpec{
			IngressClassName: &[]string{"nginx"}[0],
			Rules: []netv1.IngressRule{
				{
					Host: fmt.Sprintf("%s-%s.%s", n.Name, n.Namespace, utils.GetIngressDomainSuffix()),
					IngressRuleValue: netv1.IngressRuleValue{
						HTTP: &netv1.HTTPIngressRuleValue{
							Paths: []netv1.HTTPIngressPath{
								{
									Path:     "/",
									PathType: &[]netv1.PathType{netv1.PathTypePrefix}[0],
									Backend: netv1.IngressBackend{
										Service: &netv1.IngressServiceBackend{
											Name: n.Name,
											Port: netv1.ServiceBackendPort{
												Number: constants.DefaultServingPort,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}
		return controllerutil.SetControllerReference(n, ingress, g.scheme)
	}); err != nil {
		return err
	}

	return nil
}

func (g *IngressNginxBackend) Inject(n *systemv1alpha1.Notebook) error {
	return nil
}

func (g *IngressNginxBackend) Setup(client client.Client, scheme *runtime.Scheme) {
	g.client = client
	g.scheme = scheme
}

func (g *IngressNginxBackend) Clean(n *systemv1alpha1.Notebook) error {
	return nil
}
