package gateway

import (
	"context"
	"testing"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/utils"

	netv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/kubernetes/scheme"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func init() {
	// Register the Notebook type in the scheme
	utilruntime.Must(clientgoscheme.AddToScheme(scheme.Scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme.Scheme))
}

func TestIngressNginxBackend_Name(t *testing.T) {
	g := &IngressNginxBackend{}
	if got := g.Name(); got != "nginx-ingress" {
		t.<PERSON><PERSON><PERSON>("IngressNginxBackend.Name() = %v, want %v", got, "nginx-ingress")
	}
}

func TestIngressNginxBackend_Setup(t *testing.T) {
	g := &IngressNginxBackend{}
	mockClient := fake.NewClientBuilder().WithScheme(scheme.Scheme).Build()
	mockScheme := scheme.Scheme

	g.Setup(mockClient, mockScheme)

	if g.client != mockClient {
		t.Error("client was not set correctly")
	}
	if g.scheme != mockScheme {
		t.Error("scheme was not set correctly")
	}
}

func TestIngressNginxBackend_Configure(t *testing.T) {
	tests := []struct {
		name           string
		notebook       *systemv1alpha1.Notebook
		expectedHost   string
		expectedPath   string
		expectedPort   int32
		expectedClass  string
		expectedLabels map[string]string
	}{
		{
			name: "basic notebook configuration",
			notebook: &systemv1alpha1.Notebook{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-notebook",
					Namespace: "test-namespace",
					Labels: map[string]string{
						"app": "app",
					},
				},
			},
			expectedHost:  "test-notebook-test-namespace." + utils.GetIngressDomainSuffix(),
			expectedPath:  "/",
			expectedPort:  80,
			expectedClass: "nginx",
			expectedLabels: map[string]string{
				"app": "app",
			},
		},
		{
			name: "notebook with custom labels",
			notebook: &systemv1alpha1.Notebook{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "custom-notebook",
					Namespace: "custom-namespace",
					Labels: map[string]string{
						"custom-label": "custom-value",
						"app":          "custom-app",
					},
				},
			},
			expectedHost:  "custom-notebook-custom-namespace." + utils.GetIngressDomainSuffix(),
			expectedPath:  "/",
			expectedPort:  80,
			expectedClass: "nginx",
			expectedLabels: map[string]string{
				"custom-label": "custom-value",
				"app":          "custom-app",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create fake client with proper scheme
			mockClient := fake.NewClientBuilder().WithScheme(scheme.Scheme).Build()
			mockScheme := scheme.Scheme

			g := &IngressNginxBackend{
				client: mockClient,
				scheme: mockScheme,
			}

			// Test Configure method
			err := g.Configure(tt.notebook)
			if err != nil {
				t.Errorf("Configure() error = %v", err)
			}

			// Verify Ingress was created correctly
			ingress := &netv1.Ingress{}
			err = mockClient.Get(context.TODO(), client.ObjectKey{
				Name:      tt.notebook.Name,
				Namespace: tt.notebook.Namespace,
			}, ingress)
			if err != nil {
				t.Errorf("Failed to get created Ingress: %v", err)
			}

			// Verify Ingress configuration
			if ingress.Spec.Rules[0].Host != tt.expectedHost {
				t.Errorf("Ingress host = %v, want %v", ingress.Spec.Rules[0].Host, tt.expectedHost)
			}

			if *ingress.Spec.IngressClassName != tt.expectedClass {
				t.Errorf("IngressClassName = %v, want %v", *ingress.Spec.IngressClassName, tt.expectedClass)
			}

			// Verify path configuration
			if ingress.Spec.Rules[0].HTTP.Paths[0].Path != tt.expectedPath {
				t.Errorf("Ingress path = %v, want %v", ingress.Spec.Rules[0].HTTP.Paths[0].Path, tt.expectedPath)
			}

			// Verify port configuration
			if ingress.Spec.Rules[0].HTTP.Paths[0].Backend.Service.Port.Number != tt.expectedPort {
				t.Errorf("Ingress port = %v, want %v", ingress.Spec.Rules[0].HTTP.Paths[0].Backend.Service.Port.Number, tt.expectedPort)
			}

			// Verify CORS annotations
			expectedAnnotations := map[string]string{
				"nginx.ingress.kubernetes.io/cors-allow-credentials": "true",
				"nginx.ingress.kubernetes.io/cors-allow-headers":     "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization",
				"nginx.ingress.kubernetes.io/cors-allow-origin":      "*",
				"nginx.ingress.kubernetes.io/enable-cors":            "true",
			}

			for k, v := range expectedAnnotations {
				if ingress.Annotations[k] != v {
					t.Errorf("Annotation %s = %v, want %v", k, ingress.Annotations[k], v)
				}
			}
		})
	}
}

func TestIngressNginxBackend_Inject(t *testing.T) {
	g := &IngressNginxBackend{}
	notebook := &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-notebook",
			Namespace: "test-namespace",
		},
	}

	err := g.Inject(notebook)
	if err != nil {
		t.Errorf("Inject() error = %v", err)
	}

	// Verify that Inject doesn't modify the notebook
	if notebook.Spec.PodTemplate.Spec.ServiceAccountName != "" {
		t.Error("Inject() should not modify the notebook's ServiceAccountName")
	}
}

func TestIngressNginxBackend_Clean(t *testing.T) {
	tests := []struct {
		name     string
		notebook *systemv1alpha1.Notebook
		setup    func(client client.Client) error
	}{
		{
			name: "clean existing ingress",
			notebook: &systemv1alpha1.Notebook{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-notebook",
					Namespace: "test-namespace",
				},
			},
			setup: func(client client.Client) error {
				ingress := &netv1.Ingress{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "test-notebook",
						Namespace: "test-namespace",
					},
				}
				return client.Create(context.TODO(), ingress)
			},
		},
		{
			name: "clean non-existent ingress",
			notebook: &systemv1alpha1.Notebook{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "non-existent",
					Namespace: "test-namespace",
				},
			},
			setup: func(client client.Client) error {
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create fake client with proper scheme
			mockClient := fake.NewClientBuilder().WithScheme(scheme.Scheme).Build()
			mockScheme := scheme.Scheme

			g := &IngressNginxBackend{
				client: mockClient,
				scheme: mockScheme,
			}

			// Setup test environment
			if err := tt.setup(mockClient); err != nil {
				t.Fatalf("Failed to setup test: %v", err)
			}

			// Test Clean method
			err := g.Clean(tt.notebook)
			if err != nil {
				t.Errorf("Clean() error = %v", err)
			}
		})
	}
}
