package empty

import (
	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"

	// Third-party imports
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type EmptyBackend struct {
}

func (e *EmptyBackend) Name() string {
	return "empty"
}

func (e *EmptyBackend) Configure(n *systemv1alpha1.Notebook) error {
	return nil
}

func (e *EmptyBackend) Inject(n *systemv1alpha1.Notebook) error {
	return nil
}

func (e *EmptyBackend) Setup(client client.Client, scheme *runtime.Scheme) {
}

func (e *EmptyBackend) Clean(n *systemv1alpha1.Notebook) error {
	return nil
}
