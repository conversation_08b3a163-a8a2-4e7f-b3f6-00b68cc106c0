package assembly

import (
	// Standard library imports
	"cetccloud/cetccloud-operator/internal/component/assembly/deploy"
	"cetccloud/cetccloud-operator/internal/component/assembly/ingress"
)

type Builder interface {
	DeployBuilder() *deploy.Builder
	IngressBuilder() *ingress.Builder
}

type kubernetesBuilder struct {
	deployBuilder  *deploy.Builder
	ingressBuilder *ingress.Builder
}

func NewKubernetesBuilder() Builder {
	kb := &kubernetesBuilder{
		deployBuilder:  deploy.NewDeployBuilder(),
		ingressBuilder: ingress.NewNginxIngressBuilder(),
	}
	return kb
}

func (build *kubernetesBuilder) DeployBuilder() *deploy.Builder {
	return build.deployBuilder
}

func (build *kubernetesBuilder) IngressBuilder() *ingress.Builder {
	return build.ingressBuilder
}
