package deploy

import (
	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Builder struct{}

func NewDeployBuilder() *Builder {
	return &Builder{}
}

func (s *Builder) BuildStatefulSet(statefulSet *appsv1.StatefulSet, notebook *systemv1alpha1.Notebook) error {
	labels := statefulSet.Labels
	if labels == nil {
		labels = make(map[string]string)
	}
	labels[constants.LabelNotebookName] = notebook.Name
	labels[constants.LabelStatefulsetName] = notebook.Name
	statefulSet.Labels = labels

	podTemplate := notebook.Spec.PodTemplate
	statefulSet.Spec = appsv1.StatefulSetSpec{
		Replicas: &[]int32{1}[0],
		Selector: &metav1.LabelSelector{
			MatchLabels: map[string]string{
				constants.LabelNotebookName:    notebook.Name,
				constants.LabelStatefulsetName: notebook.Name,
			},
		},
		Template: podTemplate,
	}

	// Setup volume claim templates if needed
	if err := s.setupVolumeClaimTemplates(statefulSet, notebook); err != nil {
		return err
	}

	return nil
}

// setupVolumeClaimTemplates configures the VolumeClaimTemplates for the StatefulSet
// It checks if a PVC already exists in the pod template, and if not, creates a default one
// with the size and storage class specified in the notebook annotations or the default values
func (s *Builder) setupVolumeClaimTemplates(statefulSet *appsv1.StatefulSet, notebook *systemv1alpha1.Notebook) error {
	// Check if a PVC already exists in the pod template
	for _, volume := range notebook.Spec.PodTemplate.Spec.Volumes {
		if volume.PersistentVolumeClaim != nil {
			return nil
		}
	}

	// Determine storage size from annotation or use default
	storageSize := constants.DefaultStorageSize
	if metav1.HasAnnotation(notebook.ObjectMeta, constants.AnnotationStorageSize) {
		storageSize = notebook.Annotations[constants.AnnotationStorageSize]
	}

	// Create the VolumeClaimTemplate
	volumeClaim := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name: constants.DefaultStorageName,
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteOnce,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: resource.MustParse(storageSize),
				},
			},
		},
	}

	// Determine storage class from annotation if present
	if metav1.HasAnnotation(notebook.ObjectMeta, constants.AnnotationStorageClass) {
		storageClassName := notebook.Annotations[constants.AnnotationStorageClass]
		volumeClaim.Spec.StorageClassName = &storageClassName
	}

	// Set the VolumeClaimTemplates
	statefulSet.Spec.VolumeClaimTemplates = []corev1.PersistentVolumeClaim{volumeClaim}

	// Set PVC retention policy
	pvcRetentionPolicy := appsv1.DeletePersistentVolumeClaimRetentionPolicyType // 默认删除 PVC

	// 只有当注解存在且值为 "false" 时才保留 PVC
	if metav1.HasAnnotation(notebook.ObjectMeta, constants.AnnotationStorageAutoDelete) {
		autoDeleteValue := notebook.Annotations[constants.AnnotationStorageAutoDelete]
		if autoDeleteValue == "false" {
			pvcRetentionPolicy = appsv1.RetainPersistentVolumeClaimRetentionPolicyType
		}
	}

	statefulSet.Spec.PersistentVolumeClaimRetentionPolicy = &appsv1.StatefulSetPersistentVolumeClaimRetentionPolicy{
		WhenDeleted: pvcRetentionPolicy,
		WhenScaled:  appsv1.RetainPersistentVolumeClaimRetentionPolicyType, // 缩容时始终保留 PVC
	}

	return nil
}
