package service

import (
	"context"
	"testing"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"

	// Third-party imports
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

var nb *systemv1alpha1.Notebook
var svc *Service

func init() {
	nb = &systemv1alpha1.Notebook{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "jupyter-test",
			Namespace: "notebook-test",
		},
		Spec: systemv1alpha1.NotebookSpec{
			ModelURIS: []string{"s3://example-bucket/model"},
			PodTemplate: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{"app": "jupyter-test"},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:            "notebook",
							Image:           "kubeflownotebookswg/jupyter-scipy:v1.8.0",
							ImagePullPolicy: corev1.PullAlways,
							Ports: []corev1.ContainerPort{
								{
									Name:          "http",
									ContainerPort: 8888,
								},
							},
						},
					},
				},
			},
		},
	}
	svc = &Service{}
	var scheme = runtime.NewScheme()
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))

	objs := []client.Object{
		nb,
	}
	svc.Setup(
		fake.NewClientBuilder().WithScheme(scheme).WithObjects(objs...).Build(),
		scheme,
	)
}

func Test_Default(t *testing.T) {
	err := svc.Configure(nb)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	createdService := &corev1.Service{}
	err = svc.client.Get(context.TODO(), client.ObjectKey{Name: nb.Name, Namespace: nb.Namespace}, createdService)
	if err != nil {
		t.Errorf("Failed to get service: %v", err)
	}

	if createdService.Spec.Type != corev1.ServiceTypeNodePort {
		t.Errorf("Expected service type to be NodePort, got %s", createdService.Spec.Type)
	}

	if createdService.Spec.Selector["cetccloud.ai/notebook-name"] != nb.Name {
		t.Errorf("Expected selector to have notebook name %s, got %s",
			nb.Name, createdService.Spec.Selector["cetccloud.ai/notebook-name"])
	}

	if len(createdService.Spec.Ports) != 2 {
		t.Errorf("Expected 2 ports, got %d", len(createdService.Spec.Ports))
	} else {
		// Check HTTP port
		httpPort := createdService.Spec.Ports[0]
		if httpPort.Name != "http-"+nb.Name {
			t.Errorf("Expected HTTP port name to be 'http-%s', got '%s'", nb.Name, httpPort.Name)
		}
		if httpPort.Port != 80 {
			t.Errorf("Expected HTTP port to be 80, got %d", httpPort.Port)
		}
		if httpPort.TargetPort.IntVal != 8888 {
			t.Errorf("Expected HTTP target port to be 8888, got %d", httpPort.TargetPort.IntVal)
		}

		// Check SSH port
		sshPort := createdService.Spec.Ports[1]
		if sshPort.Name != "tcp-ssh-"+nb.Name {
			t.Errorf("Expected SSH port name to be 'tcp-ssh-%s', got '%s'", nb.Name, sshPort.Name)
		}
		if sshPort.Port != 22 {
			t.Errorf("Expected SSH port to be 22, got %d", sshPort.Port)
		}
		if sshPort.TargetPort.IntVal != 22 {
			t.Errorf("Expected SSH target port to be 22, got %d", sshPort.TargetPort.IntVal)
		}
	}

	// Check owner reference
	if len(createdService.OwnerReferences) != 1 {
		t.Errorf("Expected 1 owner reference, got %d", len(createdService.OwnerReferences))
	} else {
		ownerRef := createdService.OwnerReferences[0]
		if ownerRef.Name != nb.Name {
			t.Errorf("Expected owner reference name to be %s, got %s", nb.Name, ownerRef.Name)
		}
		if ownerRef.Kind != "Notebook" {
			t.Errorf("Expected owner reference kind to be Notebook, got %s", ownerRef.Kind)
		}
	}
}

func Test_Name(t *testing.T) {
	name := svc.Name()
	if name != "service" {
		t.Errorf("Expected name to be 'service', got %s", name)
	}
}

func Test_Inject(t *testing.T) {
	err := svc.Inject(nb)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func Test_Clean(t *testing.T) {
	err := svc.Clean(nb)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}
