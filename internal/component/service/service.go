package service

import (
	"context"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/internal/constants"

	// Third-party imports
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

type Service struct {
	client client.Client
	scheme *runtime.Scheme
}

func (s *Service) Name() string {
	return "service"
}

func (s *Service) Configure(n *systemv1alpha1.Notebook) error {
	port := constants.DefaultContainerPort
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      n.Name,
			Namespace: n.Namespace,
		},
	}

	if _, err := controllerutil.CreateOrUpdate(context.TODO(), s.client, service, func() error {
		service.Spec = corev1.ServiceSpec{
			// TODO 支持配置开启 NodePort,从notebook annotations 中获取
			Type: corev1.ServiceTypeNodePort,
			Selector: map[string]string{
				constants.LabelNotebookName:    n.Name,
				constants.LabelStatefulsetName: n.Name,
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "http-" + n.Name,
					Port:       constants.DefaultServingPort,
					TargetPort: intstr.FromInt(port),
					Protocol:   corev1.ProtocolTCP,
				},
				{
					Name:       "tcp-ssh-" + n.Name,
					Port:       constants.DefaultSSHPort,
					TargetPort: intstr.FromInt(constants.DefaultSSHPort),
					Protocol:   corev1.ProtocolTCP,
				},
			},
		}
		return controllerutil.SetControllerReference(n, service, s.scheme)
	}); err != nil {
		return err
	}

	return nil
}

func (s *Service) Inject(n *systemv1alpha1.Notebook) error {
	return nil
}

func (s *Service) Setup(client client.Client, scheme *runtime.Scheme) {
	s.client = client
	s.scheme = scheme
}

func (s *Service) Clean(n *systemv1alpha1.Notebook) error {
	return nil
}
