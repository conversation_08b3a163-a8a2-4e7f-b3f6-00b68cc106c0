{"versions": [{"id": "v2.0.0", "status": "SUPPORTED", "version": "v2.0.0", "min_version": "v2.0.0", "updated": "2024-06-20T14:22:00Z", "copyright": "cetccloud-operator", "git_commit": "7bc19c1e902751f2417135849c184577027b32c0", "kubernetes_version": {"min": "1.27.0", "max": "1.32.0"}, "services": {"inference": {"api_versions": ["serving.kserve.io/v1beta1"], "special_support": [], "builtin_frameworks": ["tensorflow", "pytorch", "onnx", "huggingface", "vllm-ascend", "sglang"]}, "notebook": {"api_versions": ["cetccloud.ai/v1alpha1"], "special_support": ["image_saver"], "builtin_frameworks": ["jup<PERSON><PERSON>", "jupyterlab", "codeserver"]}}}, {"id": "v2.1.0", "status": "CURRENT", "version": "v2.1.0", "min_version": "v2.1.0", "updated": "2025-07-03T08:39:43Z", "copyright": "cetccloud-operator", "git_commit": "78ed59f72f7317017ea5439bba4a6b1034aa1e49", "kubernetes_version": {"min": "1.27.0", "max": "1.32.0"}, "services": {"inference": {"api_versions": ["serving.kserve.io/v1beta1"], "special_support": ["custom_annotation_filter"], "builtin_frameworks": ["tensorflow", "pytorch", "onnx", "huggingface", "vllm-ascend", "sglang"]}, "notebook": {"api_versions": ["cetccloud.ai/v1alpha1"], "special_support": ["image_saver"], "builtin_frameworks": ["jup<PERSON><PERSON>", "jupyterlab", "codeserver"]}, "trainer": {"api_versions": ["trainer.kubeflow.org/v1alpha1"], "special_support": ["swift_finetuning"], "builtin_frameworks": ["pytorch", "tensorflow"]}}}]}