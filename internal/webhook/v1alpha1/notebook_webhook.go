/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	logf "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
	"sigs.k8s.io/controller-runtime/pkg/webhook/admission"

	cetccloudaiv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
)

// nolint:unused
// log is for logging in this package.
var notebooklog = logf.Log.WithName("notebook-resource")

// SetupNotebookWebhookWithManager registers the webhook for Notebook in the manager.
func SetupNotebookWebhookWithManager(mgr ctrl.Manager) error {
	return ctrl.NewWebhookManagedBy(mgr).For(&cetccloudaiv1alpha1.Notebook{}).
		WithValidator(&NotebookCustomValidator{}).
		WithDefaulter(&NotebookCustomDefaulter{}).
		Complete()
}

// TODO(user): EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!

// +kubebuilder:webhook:path=/mutate-cetccloud-ai-v1alpha1-notebook,mutating=true,failurePolicy=fail,sideEffects=None,groups=cetccloud.ai,resources=notebooks,verbs=create;update,versions=v1alpha1,name=mnotebook-v1alpha1.kb.io,admissionReviewVersions=v1

// NotebookCustomDefaulter struct is responsible for setting default values on the custom resource of the
// Kind Notebook when those are created or updated.
//
// NOTE: The +kubebuilder:object:generate=false marker prevents controller-gen from generating DeepCopy methods,
// as it is used only for temporary operations and does not need to be deeply copied.
type NotebookCustomDefaulter struct {
	// TODO(user): Add more fields as needed for defaulting
}

var _ webhook.CustomDefaulter = &NotebookCustomDefaulter{}

// Default implements webhook.CustomDefaulter so a webhook will be registered for the Kind Notebook.
func (d *NotebookCustomDefaulter) Default(ctx context.Context, obj runtime.Object) error {
	notebook, ok := obj.(*cetccloudaiv1alpha1.Notebook)

	if !ok {
		return fmt.Errorf("expected an Notebook object but got %T", obj)
	}
	notebooklog.Info("Defaulting for Notebook", "name", notebook.GetName())

	// TODO(user): fill in your defaulting logic.

	return nil
}

// TODO(user): change verbs to "verbs=create;update;delete" if you want to enable deletion validation.
// NOTE: The 'path' attribute must follow a specific pattern and should not be modified directly here.
// Modifying the path for an invalid path can cause API server errors; failing to locate the webhook.
// +kubebuilder:webhook:path=/validate-cetccloud-ai-v1alpha1-notebook,mutating=false,failurePolicy=fail,sideEffects=None,groups=cetccloud.ai,resources=notebooks,verbs=create;update,versions=v1alpha1,name=vnotebook-v1alpha1.kb.io,admissionReviewVersions=v1

// NotebookCustomValidator struct is responsible for validating the Notebook resource
// when it is created, updated, or deleted.
//
// NOTE: The +kubebuilder:object:generate=false marker prevents controller-gen from generating DeepCopy methods,
// as this struct is used only for temporary operations and does not need to be deeply copied.
type NotebookCustomValidator struct {
	// TODO(user): Add more fields as needed for validation
}

var _ webhook.CustomValidator = &NotebookCustomValidator{}

// ValidateCreate implements webhook.CustomValidator so a webhook will be registered for the type Notebook.
func (v *NotebookCustomValidator) ValidateCreate(ctx context.Context, obj runtime.Object) (admission.Warnings, error) {
	notebook, ok := obj.(*cetccloudaiv1alpha1.Notebook)
	if !ok {
		return nil, fmt.Errorf("expected a Notebook object but got %T", obj)
	}
	notebooklog.Info("Validation for Notebook upon creation", "name", notebook.GetName())

	// TODO(user): fill in your validation logic upon object creation.

	return nil, nil
}

// ValidateUpdate implements webhook.CustomValidator so a webhook will be registered for the type Notebook.
func (v *NotebookCustomValidator) ValidateUpdate(ctx context.Context, oldObj, newObj runtime.Object) (admission.Warnings, error) {
	notebook, ok := newObj.(*cetccloudaiv1alpha1.Notebook)
	if !ok {
		return nil, fmt.Errorf("expected a Notebook object for the newObj but got %T", newObj)
	}
	notebooklog.Info("Validation for Notebook upon update", "name", notebook.GetName())

	// TODO(user): fill in your validation logic upon object update.

	return nil, nil
}

// ValidateDelete implements webhook.CustomValidator so a webhook will be registered for the type Notebook.
func (v *NotebookCustomValidator) ValidateDelete(ctx context.Context, obj runtime.Object) (admission.Warnings, error) {
	notebook, ok := obj.(*cetccloudaiv1alpha1.Notebook)
	if !ok {
		return nil, fmt.Errorf("expected a Notebook object but got %T", obj)
	}
	notebooklog.Info("Validation for Notebook upon deletion", "name", notebook.GetName())

	// TODO(user): fill in your validation logic upon object deletion.

	return nil, nil
}
