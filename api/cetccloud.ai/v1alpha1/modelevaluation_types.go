/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// ModelEvaluationSpec defines the desired state of ModelEvaluation.
type ModelEvaluationSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// EvalType specifies the type of evaluation to perform.
	// +kubebuilder:validation:Enum=perf;eval
	// +kubebuilder:validation:Required
	EvalType string `json:"evalType"`

	// ModelName is the name of the model to be evaluated.
	// +kubebuilder:validation:Required
	ModelName string `json:"modelName"`

	// ApiUrl is the URL of the model API endpoint.
	// +kubebuilder:validation:Required
	ApiUrl string `json:"apiUrl"`

	// ApiKey is the API key for accessing the model API.
	// +optional
	ApiKey string `json:"apiKey,omitempty"`

	// Headers contains additional HTTP headers to be sent with API requests.
	// +optional
	Headers map[string]string `json:"headers,omitempty"`

	// Parallel specifies the number of parallel requests to make during evaluation.
	// +optional
	// +kubebuilder:default=1
	Parallel int `json:"parallel,omitempty"`

	// Number specifies the number of evaluation samples to use.
	// +optional
	// +kubebuilder:default=100
	Number int `json:"number,omitempty"`

	// Image is the container image to be used for the evaluation Job.
	// +optional
	Image string `json:"image"`

	// Resources defines the resource requests and limits for the evaluation Job.
	// +optional
	Resources corev1.ResourceRequirements `json:"resources,omitempty"`

	// Storage URI for the model address
	// +optional
	ModelURIS []string `json:"modelURIS,omitempty"`

	// DataSet URI for the dataset address
	// +optional
	DataSetURIS []string `json:"dataSetURIS,omitempty"`

	// BackoffLimit specifies the number of retries before marking the job as failed.
	// +optional
	// +kubebuilder:default=0
	BackoffLimit *int32 `json:"backoffLimit,omitempty"`

	// WebhookUrl is the URL where evaluation results will be sent via webhook.
	// If not specified, the default webhook URL will be used.
	// +optional
	WebhookUrl string `json:"webhookUrl,omitempty"`

	// ConnectTimeout specifies the connection timeout in seconds for API requests.
	// +optional
	// +kubebuilder:default=20
	ConnectTimeout int `json:"connectTimeout,omitempty"`

	// ReadTimeout specifies the read timeout in seconds for API requests.
	// +optional
	// +kubebuilder:default=20
	ReadTimeout int `json:"readTimeout,omitempty"`

	// Temperature controls randomness in the model's responses. Lower values make responses more deterministic.
	// +optional
	// +kubebuilder:default="0.0"
	Temperature string `json:"temperature,omitempty"`

	// TopP controls nucleus sampling. Only tokens with cumulative probability up to this value are considered.
	// +optional
	TopP string `json:"topP,omitempty"`

	// TopK controls top-k sampling. Only the top k most likely tokens are considered for generation.
	// +optional
	TopK string `json:"topK,omitempty"`

	// API specifies which API interface to use for model evaluation.
	// +optional
	// +kubebuilder:default="openai"
	API string `json:"api,omitempty"`

	// LogEveryNQuery specifies how often to log progress during evaluation.
	// +optional
	// +kubebuilder:default=10
	LogEveryNQuery int `json:"logEveryNQuery,omitempty"`
}

// EvaluationResults defines the structure for evaluation results
type EvaluationResults struct {
	// Timestamp when the results were generated
	// +optional
	Timestamp string `json:"timestamp,omitempty"`

	// Summary contains key metrics or findings from the evaluation
	// +optional
	Summary string `json:"summary,omitempty"`

	// DownloadUrl is the URL where the evaluation results can be downloaded
	// +optional
	DownloadUrl string `json:"downloadUrl,omitempty"`
}

// ModelEvaluationStatus defines the observed state of ModelEvaluation.
type ModelEvaluationStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Phase is the current phase of the evaluation. (e.g., Pending, Running, Succeeded, Failed)
	// +optional
	Phase ModelEvaluationPhase `json:"phase,omitempty"`

	// Conditions represent the latest available observations of the ModelEvaluation's state
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	Conditions []metav1.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type"`

	// Results contains the evaluation results when the job completes successfully
	// +optional
	Results *EvaluationResults `json:"results,omitempty"`
}

// +kubebuilder:object:root=true
// +kubebuilder:resource:shortName=ej
// +kubebuilder:subresource:status
// +kubebuilder:printcolumn:name="Phase",type="string",JSONPath=".status.phase"
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// ModelEvaluation is the Schema for the modelevaluations API.
type ModelEvaluation struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ModelEvaluationSpec   `json:"spec,omitempty"`
	Status ModelEvaluationStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// ModelEvaluationList contains a list of ModelEvaluation.
type ModelEvaluationList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ModelEvaluation `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ModelEvaluation{}, &ModelEvaluationList{})
}

// ModelEvaluationPhase defines the phase of a model evaluation
type ModelEvaluationPhase string

// Model Evaluation Phases
const (
	ModelEvaluationPhasePending           ModelEvaluationPhase = "Pending"
	ModelEvaluationPhaseRunning           ModelEvaluationPhase = "Running"
	ModelEvaluationPhaseWaitingForResults ModelEvaluationPhase = "WaitingForResults"
	ModelEvaluationPhaseSucceeded         ModelEvaluationPhase = "Succeeded"
	ModelEvaluationPhaseFailed            ModelEvaluationPhase = "Failed"
	ModelEvaluationPhaseTerminating       ModelEvaluationPhase = "Terminating"
)
