/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (

	// Third-party imports
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// NotebookImageSaverSpec defines the desired state of Notebook.

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// NotebookImageSaverSpec defines the desired state of NotebookImageSaver
type NotebookImageSaverSpec struct {
	// NotebookName is the name of the notebook to save image from
	NotebookName string `json:"notebookName"`

	// NotebookNamespace is the namespace of the notebook
	// +optional
	NotebookNamespace string `json:"notebookNamespace,omitempty"`

	// ContainerName is the name of the container to save image from
	// +optional
	ContainerName string `json:"containerName,omitempty"`

	// Repository for the image repository
	Repository string `json:"repository"`

	// Tag for the image tag
	Tag string `json:"tag"`

	// RegistrySecret is the secret name for registry authentication
	// +optional
	RegistrySecret string `json:"registrySecret,omitempty"`

	// ImageSaverImage is the image used for the image saver job
	// +optional
	ImageSaverImage string `json:"imageSaverImage,omitempty"`
}

// NotebookImageSaverStatus defines the observed state of NotebookImageSaver
type NotebookImageSaverStatus struct {
	// Phase represents the current phase of the image saving process
	Phase NotebookImageSaverPhase `json:"phase,omitempty"`

	// Conditions is an array of current conditions
	Conditions []NotebookImageSaverCondition `json:"conditions,omitempty"`

	// JobName is the name of the job created for image saving
	JobName string `json:"jobName,omitempty"`

	// NodeName is the name of the node where the notebook is running
	NodeName string `json:"nodeName,omitempty"`

	// ContainerID is the ID of the container to save
	ContainerID string `json:"containerID,omitempty"`

	// ImageDigest is the digest of the saved image
	ImageDigest string `json:"imageDigest,omitempty"`

	// Message provides additional information about the current state
	Message string `json:"message,omitempty"`

	// StartTime is when the image saving process started
	StartTime *metav1.Time `json:"startTime,omitempty"`

	// CompletionTime is when the image saving process completed
	CompletionTime *metav1.Time `json:"completionTime,omitempty"`
}

// NotebookImageSaverPhase represents the phase of the image saving process
type NotebookImageSaverPhase string

const (
	// NotebookImageSaverPhasePending indicates the image saver is pending
	NotebookImageSaverPhasePending NotebookImageSaverPhase = "Pending"
	// NotebookImageSaverPhaseRunning indicates the image saving is in progress
	NotebookImageSaverPhaseRunning NotebookImageSaverPhase = "Running"
	// NotebookImageSaverPhaseSucceeded indicates the image saving completed successfully
	NotebookImageSaverPhaseSucceeded NotebookImageSaverPhase = "Succeeded"
	// NotebookImageSaverPhaseFailed indicates the image saving failed
	NotebookImageSaverPhaseFailed NotebookImageSaverPhase = "Failed"
	// NotebookImageSaverPhaseTerminating indicates the image saving delete
	NotebookImageSaverPhaseTerminating NotebookImageSaverPhase = "Terminating"
)

type NotebookImageSaverCondition struct {
	// Type is the type of the condition
	Type NotebookImageSaverConditionType `json:"type"`
	// Status is the status of the condition. Can be True, False, Unknown.
	Status corev1.ConditionStatus `json:"status"`
	// Last time we probed the condition.
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty"`
	// Last time the condition transitioned from one status to another.
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`
	// (brief) reason the container is in the current state
	// +optional
	Reason string `json:"reason,omitempty"`
	// Message regarding why the container is in the current state.
	// +optional
	Message string `json:"message,omitempty"`
}

// NotebookImageSaverConditionType represents the type of condition
type NotebookImageSaverConditionType string

const (
	// NotebookImageSaverConditionReady indicates whether the image saver is ready
	NotebookImageSaverConditionReady NotebookImageSaverConditionType = "Ready"
	// NotebookImageSaverConditionJobCreated indicates whether the job has been created
	NotebookImageSaverConditionJobCreated NotebookImageSaverConditionType = "JobCreated"
	// NotebookImageSaverConditionImageSaved indicates whether the image has been saved
	NotebookImageSaverConditionImageSaved NotebookImageSaverConditionType = "ImageSaved"
)

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:path=notebookimagesavers,singular=notebookimagesaver,scope=Namespaced
// +kubebuilder:resource:shortName=nbis
// +kubebuilder:printcolumn:name="status",type="string",JSONPath=".status.status"
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"
// +kubebuilder:printcolumn:name="URL",type="string",JSONPath=".status.url"
// +kubebuilder:printcolumn:name="Ready",type="string",JSONPath=".status.conditions[?(@.type=='Ready')].status"

type NotebookImageSaver struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   NotebookImageSaverSpec   `json:"spec,omitempty"`
	Status NotebookImageSaverStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// NotebookImageSaverList contains a list of NotebookImageSaver
type NotebookImageSaverList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []NotebookImageSaver `json:"items"`
}

func init() {
	SchemeBuilder.Register(&NotebookImageSaver{}, &NotebookImageSaverList{})
}
