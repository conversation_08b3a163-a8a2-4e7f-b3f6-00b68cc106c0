FROM docker.1ms.run/python:3.11

WORKDIR /workspace
# 配置 Debian 国内镜像源
RUN echo "deb https://mirrors.aliyun.com/debian/ bookworm main" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bookworm-updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security/ bookworm-security main" >> /etc/apt/sources.list
RUN apt-get update && apt-get install -y \
    vim \
    curl \
    wget \
    net-tools \
    iputils-ping \
    && rm -rf /var/lib/apt/lists/*

ENV PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
RUN pip install --upgrade pip
RUN pip install 'evalscope[all]'
RUN pip install sse_starlette

RUN pip install modelscope

ENV MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
RUN mkdir -p $MODELSCOPE_CACHE
# HC3-Chinese dateset
RUN modelscope download --dataset AI-ModelScope/HC3-Chinese