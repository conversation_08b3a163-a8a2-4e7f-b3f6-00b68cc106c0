

import os
import sys
import time
from urllib.parse import urlparse
from evalscope.perf.main import run_perf_benchmark
from evalscope.perf.arguments import Arguments
import json
import argparse
import requests
eval_type = os.environ.get('EVAL_TYPE')
if not eval_type:
    print("Error: EVAL_TYPE environment variable not found")
    sys.exit(1)

# Check required environment variables
model_name = os.environ.get('MODEL_NAME')
if not model_name:
    print("Error: MODEL_NAME environment variable not found")
    sys.exit(1)
api_url = os.environ.get('API_URL')
if not api_url:
    print("Error: API_URL environment variable not found")
    sys.exit(1)
# Validate API_URL format
try:
    parsed_url = urlparse(api_url)
    if not all([parsed_url.scheme, parsed_url.netloc]):
        print(f"Error: Invalid API_URL format: {api_url}")
        sys.exit(1)
    print(f"Using API URL: {api_url}")
except Exception as e:
    print(f"Error: API_URL validation failed: {e}")
    sys.exit(1)

# Get API_KEY environment variable
api_key = os.environ.get('API_KEY', 'apikey')
if 'API_KEY' not in os.environ:
    print("API_KEY environment variable not found, using default value: apikey")

# 获取超时配置，提供默认值
connect_timeout = int(os.environ.get('CONNECT_TIMEOUT', '20'))
read_timeout = int(os.environ.get('READ_TIMEOUT', '20'))

# 获取模型推理参数
temperature = float(os.environ.get('TEMPERATURE', '0.0'))
top_p = os.environ.get('TOP_P')
top_k = os.environ.get('TOP_K')
api_type = os.environ.get('API', 'openai')

print(f"Using connect_timeout: {connect_timeout}s, read_timeout: {read_timeout}s")
print(f"Using temperature: {temperature}, top_p: {top_p}, top_k: {top_k}, api: {api_type}")

# 构建 Arguments 对象
task_cfg_args = {
    'model': model_name,
    'connect_timeout': connect_timeout,
    'read_timeout': read_timeout,
    'dataset_path': '/mnt/workspace/.cache/modelscope/datasets/AI-ModelScope/HC3-Chinese/open_qa.jsonl',
    'url': api_url,
    'api_key': api_key,
    'log_every_n_query': int(os.environ.get('LOG_EVERY_N_QUERY', '10')),
    'api': api_type,
    'dataset': 'openqa',
    'parallel': int(os.environ.get('PARALLEL', '1')),
    'number': int(os.environ.get('NUMBER', '100')),
    'stream': True,
    'temperature': temperature,
}

# 只有当 top_p 和 top_k 有值时才添加
if top_p:
    task_cfg_args['top_p'] = float(top_p)
if top_k:
    task_cfg_args['top_k'] = int(top_k)

task_cfg = Arguments(**task_cfg_args)

def send_webhook_result(task_name, results_data, max_retries=3, retry_delay=5):
    """Send evaluation results to webhook endpoint with retry mechanism"""
    webhook_url = os.environ.get('WEBHOOK_URL')
    if not webhook_url:
        print("Warning: WEBHOOK_URL environment variable not found, skipping webhook")
        return

    payload = {
        "task_name": task_name,
        "results": results_data
    }

    last_error = None
    for attempt in range(max_retries):
        try:
            print(f"Sending webhook request (attempt {attempt + 1}/{max_retries}) to: {webhook_url}")
            response = requests.post(webhook_url, json=payload, timeout=30)

            if response.status_code == 200:
                print(f"Successfully sent results to webhook: {webhook_url}")
                return
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                print(f"Webhook request failed: {error_msg}")
                last_error = Exception(f"Failed to send webhook: {error_msg}")

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error: {e}"
            print(f"Webhook request failed: {error_msg}")
            last_error = Exception(f"Error sending webhook: {error_msg}")

        # Wait before retry (except for the last attempt)
        if attempt < max_retries - 1:
            print(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)

    # All retries failed
    print(f"FATAL: Failed to send webhook after {max_retries} attempts")
    raise last_error

if eval_type == 'perf':
    results = run_perf_benchmark(task_cfg)
    output_dir = task_cfg.outputs_dir
    print(f"output_dir: {output_dir}")
    summary_file = os.path.join(output_dir, "benchmark_summary.json")
    
    # Parse results and send to webhook if successful
    with open(summary_file, 'r') as f:
        results_data = json.load(f)
        print(results_data)
        
        # Get task name from environment variable
        task_name = os.environ.get('TASK_NAME')
        if task_name:
            try:
                send_webhook_result(task_name, results_data)
                print("Evaluation completed successfully and results sent to webhook")
            except Exception as e:
                print(f"FATAL: Failed to send webhook results: {e}")
                sys.exit(1)
        else:
            print("Error: TASK_NAME environment variable not found, cannot send webhook")
            sys.exit(1)

else:
    # TODO: Implement run_task method for other evaluation types
    print(f"TODO: run_task method not implemented for EVAL_TYPE '{eval_type}'")
    sys.exit(1)