#!/bin/bash

# 多架构 Docker 镜像构建脚本
# 支持 linux/amd64 和 linux/arm64 架构

set -e

# 配置变量
IMAGE_NAME="evalscope"
IMAGE_TAG="v1.2.0"
REGISTRY=""  # 如果需要推送到注册表，在这里设置
PLATFORMS="linux/amd64,linux/arm64"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 解析命令行参数
PUSH=false
NO_CACHE=false
SINGLE_ARCH=false
HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --push)
            PUSH=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --single-arch)
            SINGLE_ARCH=true
            PLATFORMS=$(uname -m | sed 's/x86_64/linux\/amd64/; s/aarch64/linux\/arm64/; s/arm64/linux\/arm64/')
            shift
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        *)
            print_error "未知参数: $1"
            HELP=true
            shift
            ;;
    esac
done

# 显示帮助信息
if [ "$HELP" = true ]; then
    echo "多架构 Docker 镜像构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --push              构建后推送到注册表"
    echo "  --no-cache          不使用构建缓存"
    echo "  --single-arch       只构建当前架构"
    echo "  --registry <url>    设置注册表 URL"
    echo "  --tag <tag>         设置镜像标签 (默认: v1.2.0)"
    echo "  --help, -h          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 构建多架构镜像"
    echo "  $0 --single-arch --no-cache          # 构建当前架构，不使用缓存"
    echo "  $0 --push --registry myregistry.com  # 构建并推送到注册表"
    exit 0
fi

# 设置完整的镜像名称
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

print_step "开始多架构 Docker 镜像构建"
echo "镜像名称: $FULL_IMAGE_NAME"
echo "目标平台: $PLATFORMS"
echo "推送到注册表: $PUSH"
echo "使用缓存: $([ "$NO_CACHE" = true ] && echo "否" || echo "是")"

# 检查 Docker Buildx
print_step "检查 Docker Buildx 支持"
if ! docker buildx version >/dev/null 2>&1; then
    print_error "Docker Buildx 未安装或不可用"
    print_info "请安装 Docker Desktop 或启用 Buildx 插件"
    exit 1
fi

# 创建并使用 buildx builder
print_step "设置 Docker Buildx Builder"
BUILDER_NAME="evalscope-builder"

# 检查 builder 是否存在
if ! docker buildx inspect $BUILDER_NAME >/dev/null 2>&1; then
    print_info "创建新的 builder: $BUILDER_NAME"
    docker buildx create --name $BUILDER_NAME --driver docker-container --use
else
    print_info "使用现有的 builder: $BUILDER_NAME"
    docker buildx use $BUILDER_NAME
fi

# 启动 builder
docker buildx inspect --bootstrap

# 构建参数
BUILD_ARGS=""
if [ "$NO_CACHE" = true ]; then
    BUILD_ARGS="$BUILD_ARGS --no-cache"
fi

if [ "$PUSH" = true ]; then
    BUILD_ARGS="$BUILD_ARGS --push"
else
    BUILD_ARGS="$BUILD_ARGS --load"
fi

# 执行构建
print_step "开始构建镜像"
print_info "构建命令: docker buildx build --platform $PLATFORMS $BUILD_ARGS -t $FULL_IMAGE_NAME ."

if docker buildx build \
    --platform $PLATFORMS \
    $BUILD_ARGS \
    -t $FULL_IMAGE_NAME \
    --progress=plain \
    .; then
    
    print_info "✅ 镜像构建成功!"
    
    # 如果没有推送，显示本地镜像信息
    if [ "$PUSH" = false ]; then
        print_step "验证构建的镜像"
        
        if [ "$SINGLE_ARCH" = true ]; then
            # 单架构构建，可以直接运行测试
            print_info "测试镜像..."
            docker run --rm $FULL_IMAGE_NAME pip list | grep evalscope || true
        else
            # 多架构构建，显示镜像信息
            print_info "多架构镜像已构建，使用以下命令查看:"
            echo "docker buildx imagetools inspect $FULL_IMAGE_NAME"
        fi
    fi
    
    print_info "🎉 构建完成!"
    echo ""
    echo "使用方法:"
    echo "  docker run --rm $FULL_IMAGE_NAME"
    echo "  docker run --rm -it $FULL_IMAGE_NAME bash"
    
else
    print_error "❌ 镜像构建失败"
    exit 1
fi
