#!/bin/bash





# 设置基本环境变量
export EVAL_TYPE="perf"
export MODEL_NAME="Qwen-32B"
export API_URL="http://10.200.9.249:16780/openai/v1/chat/completions"
export API_KEY="75d166db-c213-45d7-ac11-f74ef0b3b294"
export WEBHOOK_URL="http://127.0.0.1:8082/webhook/result"
export JOB_NAME="test-job"
export NAMESPACE="default"
export PARALLEL=1
export NUMBER=2
export TASK_NAME="modelevaluation-sample"

echo "环境变量设置完成:"
echo "EVAL_TYPE: $EVAL_TYPE"
echo "MODEL_NAME: $MODEL_NAME"
echo "API_URL: $API_URL"
echo "API_KEY: $API_KEY"

python model_eval.py





