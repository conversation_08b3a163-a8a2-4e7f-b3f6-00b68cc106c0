
# 多架构构建支持
FROM python:3.11

# 构建参数 - 支持多架构
ARG TARGETPLATFORM
ARG BUILDPLATFORM
ARG TARGETARCH
ARG TARGETOS

# 设置工作目录
WORKDIR /workspace

# 添加标签
LABEL maintainer="evalscope-user"
LABEL description="EvalScope Performance Testing Image - Multi-Architecture"
LABEL version="1.2"
LABEL architecture="${TARGETARCH}"

# 显示构建信息
RUN echo "Building for platform: ${TARGETPLATFORM}" && \
    echo "Building on platform: ${BUILDPLATFORM}" && \
    echo "Target architecture: ${TARGETARCH}" && \
    echo "Target OS: ${TARGETOS}"

# 安装系统依赖 - 针对不同架构优化
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 清理 pip 缓存并升级
RUN pip cache purge || true
RUN pip install --no-cache-dir --upgrade pip

# 根据架构选择不同的安装策略
RUN if [ "${TARGETARCH}" = "arm64" ]; then \
        echo "Installing for ARM64 architecture"; \
        # 先安装基础版本，避免依赖冲突
        pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ evalscope==0.16.0; \
        # 然后安装核心依赖，跳过有问题的包
        pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ \
            torch torchvision transformers datasets accelerate \
            matplotlib pandas pillow requests pyyaml \
            openai nltk scikit-learn seaborn tabulate || true; \
    else \
        echo "Installing for AMD64 architecture"; \
        pip install --no-cache-dir evalscope[all]==0.16.0; \
    fi

# 验证安装
RUN pip list | grep evalscope

# 安装 ModelScope 用于数据集下载（使用阿里云镜像源）
RUN pip install  -i https://mirrors.aliyun.com/pypi/simple/ modelscope

# 设置 ModelScope 缓存目录
ENV MODELSCOPE_CACHE=/mnt/workspace/.cache/modelscope
RUN mkdir -p $MODELSCOPE_CACHE

# 测试数据集
RUN modelscope download --dataset AI-ModelScope/HC3-Chinese 
RUN modelscope download --dataset AI-ModelScope/LongAlpaca-12k
RUN modelscope download --dataset clip-benchmark/wds_flickr8k

# 设置默认命令
CMD ["evalscope", "perf", "--help"]
                                    