
ARG CUDA_VERSION=12.1.0
ARG from=nvidia/cuda:${CUDA_VERSION}-cudnn8-devel-ubuntu22.04

FROM ${from} as base

ARG DEBIAN_FRONTEND=noninteractive

ARG DEBIAN_FRONTEND=noninteractive
RUN <<EOF
apt update -y && apt upgrade -y && apt install -y --no-install-recommends  \
    git \
    git-lfs \
    python3 \
    python3-pip \
    python3-dev \
    wget \
    vim \
    libsndfile1 \
    ccache \
    software-properties-common \
    ffmpeg \
&& rm -rf /var/lib/apt/lists/*
EOF

RUN wget https://github.com/Kitware/CMake/releases/download/v3.26.1/cmake-3.26.1-Linux-x86_64.sh \
    -q -O /tmp/cmake-install.sh \
    && chmod u+x /tmp/cmake-install.sh \
    && mkdir /opt/cmake-3.26.1 \
    && /tmp/cmake-install.sh --skip-license --prefix=/opt/cmake-3.26.1 \
    && rm /tmp/cmake-install.sh \
    && ln -s /opt/cmake-3.26.1/bin/* /usr/local/bin

    
RUN ln -s /usr/bin/python3 /usr/bin/python

RUN git lfs install

FROM base as dev

WORKDIR /

RUN mkdir -p /data/shared/Qwen

WORKDIR /data/shared/Qwen/

FROM dev as bundle_req
RUN --mount=type=cache,target=/root/.cache/pip pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple    
RUN --mount=type=cache,target=/root/.cache/pip pip3 install networkx==3.1
RUN --mount=type=cache,target=/root/.cache/pip pip3 install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 xformers==0.0.29.post2
RUN --mount=type=cache,target=/root/.cache/pip pip3 install git+https://github.com/BakerBunker/transformers.git@qwen25omni  \
    && pip3 install accelerate qwen-omni-utils modelscope_studio

FROM bundle_req as bundle_vllm

ARG BUNDLE_FLASH_ATTENTION=true

# 优化编译参数 - 根据48核CPU进行优化
ENV MAX_JOBS=32
ENV NVCC_THREADS=16
ENV TORCH_CUDA_ARCH_LIST="8.6 8.9 9.0+PTX"
ENV VLLM_FA_CMAKE_GPU_ARCHES="86-real;90-real"
ENV CCACHE_DIR=/root/.cache/ccache
ENV CCACHE_MAXSIZE=20G
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
ENV TORCH_DISTRIBUTED_DEBUG=INFO
ENV PYTHONUNBUFFERED=1
ENV CUDA_MODULE_LOADING=LAZY
# ENV VLLM_INSTALL_PUNICA_KERNELS=0
# 额外优化参数
ENV TCMALLOC_LARGE_ALLOC_REPORT_THRESHOLD=10GB
ENV NCCL_ASYNC_ERROR_HANDLING=1
ENV MAKEFLAGS="-j32"

# 使用预编译的flash-attention
RUN --mount=type=cache,target=/root/.cache/ccache \
    --mount=type=cache,target=/root/.cache/pip \
    if [ "$BUNDLE_FLASH_ATTENTION" = "true" ]; then \
        pip install flash-attn==2.7.4.post1 --no-build-isolation; \
    fi


ARG BUNDLE_VLLM=true

RUN --mount=type=cache,target=/root/.cache/ccache \
    --mount=type=cache,target=/root/.cache/pip \
    if [ "$BUNDLE_VLLM" = "true" ]; then \
    mkdir -p /data/shared/code \
        && cd /data/shared/code \
        && git clone -b qwen2_omni_public_v1 https://ghfast.top/https://github.com/fyabc/vllm.git \
        && cd vllm \
        && python3 use_existing_torch.py \
        && pip3 install setuptools_scm \
        && pip3 install -r requirements/cuda.txt \
        && python3 -m pip install . --no-build-isolation --verbose \
        && cd /data/shared/Qwen \
        && rm -rf /data/shared/code/vllm; \
    fi

RUN --mount=type=cache,target=/root/.cache/pip \
    pip3 install \
    gradio==5.23.1 \
    gradio_client==1.8.0 \
    librosa==0.11.0 \
    ffmpeg==1.4 \
    ffmpeg-python==0.2.0 \
    soundfile==0.13.1 \
    av

RUN pip uninstall transformers -y &&  pip install git+https://github.com/huggingface/transformers@f742a644ca32e65758c3adb36225aef1731bd2a8
RUN rm -rvf /root/.cache/pip


EXPOSE 80

