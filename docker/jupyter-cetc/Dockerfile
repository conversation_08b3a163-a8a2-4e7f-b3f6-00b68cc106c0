#
# NOTE: Use the Makefiles to build this image correctly.
#

ARG BASE_IMG=<jupyter>
FROM ************:5000/notebook-test/jupyter-scipy-origin:v1.8.0

# install - usefull linux packages
USER root

# args - software versions
#  - TensorFlow CUDA version matrix: https://www.tensorflow.org/install/source#gpu
#  - Extra PyPi from NVIDIA (for TensorRT): https://pypi.nvidia.com/
#  - TODO: TensorRT will be removed from TensorFlow in 2.18.0
#          when updating past that version, either remove all TensorRT related packages,
#          or investigate if there is a way to keep TensorRT support
ARG TENSORFLOW_VERSION=2.17.1
ARG TENSORRT_VERSION=8.6.1.post1
ARG TENSORRT_LIBS_VERSION=8.6.1
ARG TENSORRT_BINDINGS_VERSION=8.6.1
ARG PYTORCH_VERSION=2.5.1
ARG TORCHAUDIO_VERSION=2.5.1
ARG TORCHVISION_VERSION=0.20.1
ARG MS_VERSION=2.6.0rc1

# nvidia container toolkit
# https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/docker-specialized.html
ENV NVIDIA_VISIBLE_DEVICES all
ENV NVIDIA_DRIVER_CAPABILITIES compute,utility
ENV NVIDIA_REQUIRE_CUDA "cuda>=12.3"

# install - tensorflow
#  - About '[and-cuda]' option: https://github.com/tensorflow/tensorflow/blob/v2.17.1/tensorflow/tools/pip_package/setup.py#L153-L164
#  - TODO: when updating TensorRT, you might need to change `tensorrt`, `tensorrt-libs`, and `tensorrt-bindings`
#          to `tensorrt-cu12`, `tensorrt-cu12-libs`, and `tensorrt-cu12-bindings` respectively
RUN python3 -m pip install --quiet --no-cache-dir --extra-index-url https://pypi.nvidia.com \
    tensorflow[and-cuda]==${TENSORFLOW_VERSION} \
    tensorrt==${TENSORRT_VERSION} \
    tensorrt-libs==${TENSORRT_LIBS_VERSION} \
    tensorrt-bindings==${TENSORRT_BINDINGS_VERSION} \
 && python3 -m pip install --quiet --no-cache-dir --index-url https://download.pytorch.org/whl/cu124 \
    torch==${PYTORCH_VERSION} \
    torchaudio==${TORCHAUDIO_VERSION} \
    torchvision==${TORCHVISION_VERSION} \
 && python3 -m pip install --quiet --no-cache-dir \
    tensorflow==${TENSORFLOW_VERSION} \
 && python3 -m pip install --quiet --no-cache-dir --index-url https://download.pytorch.org/whl/cpu --extra-index-url https://pypi.org/simple \
    torch==${PYTORCH_VERSION} \
    torchaudio==${TORCHAUDIO_VERSION} \
    torchvision==${TORCHVISION_VERSION} \
 && python3 -m pip install --quiet --no-cache-dir --extra-index-url https://download.pytorch.org/whl/cpu  \
    safetensors==0.5.3 \
 && pip install https://ms-release.obs.cn-north-4.myhuaweicloud.com/${MS_VERSION}/MindSpore/unified/x86_64/mindspore-${MS_VERSION/-/}-cp311-cp311-linux_x86_64.whl --trusted-host ms-release.obs.cn-north-4.myhuaweicloud.com -i https://pypi.tuna.tsinghua.edu.cn/simple

# create symlinks for TensorRT libs
#  - https://github.com/tensorflow/tensorflow/issues/61986#issuecomment-1880489731
#  - We are creating symlinks for the following libs, as this is where TF looks for them:
#     - libnvinfer.so.8.6.1 -> libnvinfer.so.8
#     - libnvinfer_plugin.so.8.6.1 -> libnvinfer_plugin.so.8
ENV PYTHON_SITE_PACKAGES /opt/conda/lib/python3.11/site-packages
ENV TENSORRT_LIBS ${PYTHON_SITE_PACKAGES}/tensorrt_libs
RUN ln -s ${TENSORRT_LIBS}/libnvinfer.so.${TENSORRT_LIBS_VERSION%%.*} ${TENSORRT_LIBS}/libnvinfer.so.${TENSORRT_LIBS_VERSION} \
 && ln -s ${TENSORRT_LIBS}/libnvinfer_plugin.so.${TENSORRT_LIBS_VERSION%%.*} ${TENSORRT_LIBS}/libnvinfer_plugin.so.${TENSORRT_LIBS_VERSION}

# envs - cudnn, tensorrt
ENV LD_LIBRARY_PATH ${LD_LIBRARY_PATH}/nvidia/cudnn/lib:${TENSORRT_LIBS}

RUN apt-get -yq update \
 && apt-get -yq install --no-install-recommends \
    # for latex
    # cm-super \
    # dvipng \
    # for matplotlib
    # ffmpeg \
    # for ssh \
    openssh-client \
    openssh-server \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/* \
 && mkdir -p /run/sshd \
 && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
 && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
 && curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.deb.sh | bash \
 && apt-get install git-lfs

# s6 - copy scripts
COPY --chown=root:root --chmod=755 s6 /etc

# install - requirements.txt
COPY requirements.txt /tmp
RUN python3 -m pip install -r /tmp/requirements.txt --quiet --no-cache-dir \
 && rm -f /tmp/requirements.txt