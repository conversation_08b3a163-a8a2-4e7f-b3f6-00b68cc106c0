ARG TARGETARCH
FROM ubuntu:22.04

# 设置时区与依赖（可选）
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    iproute2 \
    iputils-ping \
    vim \
    && rm -rf /var/lib/apt/lists/*


# Install nerdctl
ARG NERDCTL_VERSION=1.7.7
ARG TARGETARCH
RUN curl -sSL "https://github.com/containerd/nerdctl/releases/download/v${NERDCTL_VERSION}/nerdctl-${NERDCTL_VERSION}-linux-${TARGETARCH}.tar.gz" | \
    tar -xz -C /usr/local/bin/ nerdctl && \
    chmod +x /usr/local/bin/nerdctl

# Copy the image saver script
COPY image-saver.sh /usr/local/bin/image-saver.sh
RUN chmod +x /usr/local/bin/image-saver.sh

# Set working directory
WORKDIR /app

# Set the entrypoint
ENTRYPOINT ["/usr/local/bin/image-saver.sh"]
