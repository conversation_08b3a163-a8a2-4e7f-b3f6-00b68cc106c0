#!/bin/bash

set -e

# Environment variables
CONTAINER_ID=${CONTAINER_ID:-}
IMAGE_NAME=${IMAGE_NAME:-}
CRI_SOCKET=${CRI_SOCKET:-/run/k3s/containerd/containerd.sock}
REGISTRY_SECRET=${REGISTRY_SECRET:-}
NERDCTL_NAMESPACE=${NERDCTL_NAMESPACE:-k8s.io}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate required environment variables
validate_env() {
    if [ -z "$CONTAINER_ID" ]; then
        log_error "CONTAINER_ID environment variable is required"
        exit 1
    fi

    if [ -z "$IMAGE_NAME" ]; then
        log_error "IMAGE_NAME environment variable is required"
        exit 1
    fi
}

# Check if containerd socket exists
check_containerd_socket() {
    if [ ! -S "$CRI_SOCKET" ]; then
        log_error "Containerd socket not found at $CRI_SOCKET"
        exit 1
    fi
    log_info "Containerd socket found at $CRI_SOCKET"
}

# Function to run nerdctl commands
run_nerdctl() {
    nerdctl --address "$CRI_SOCKET" --namespace "$NERDCTL_NAMESPACE" "$@"
}

# Check if container exists
check_container() {
    log_info "Checking if container exists..."
    if ! run_nerdctl container ls -a | grep -q "$CONTAINER_ID"; then
        log_error "Container $CONTAINER_ID not found"
        exit 1
    fi
    log_info "Container $CONTAINER_ID found"
}

# Commit container to create a new image
commit_container() {
    log_info "Committing container to image: $IMAGE_NAME"
    
    # Get container info
    CONTAINER_INFO=$(run_nerdctl container inspect "$CONTAINER_ID" 2>/dev/null || echo "")
    if [ -z "$CONTAINER_INFO" ]; then
        log_error "Failed to inspect container $CONTAINER_ID"
        exit 1
    fi
    
    # Commit the container
    if ! run_nerdctl commit "$CONTAINER_ID" "$IMAGE_NAME"; then
        log_error "Failed to commit container to image"
        exit 1
    fi
    
    log_info "Container committed successfully to image: $IMAGE_NAME"
}

# Setup registry authentication if secret is provided
setup_registry_auth() {
    if [ -n "$REGISTRY_SECRET" ]; then
        log_info "Setting up registry authentication..."
        
        # Check if secret exists in the pod
        SECRET_PATH="/var/run/secrets/kubernetes.io/serviceaccount"
        if [ -d "$SECRET_PATH" ]; then
            # Try to read registry credentials from mounted secret
            if [ -f "/etc/registry-secret/username" ] && [ -f "/etc/registry-secret/password" ]; then
                REGISTRY_USERNAME=$(cat /etc/registry-secret/username)
                REGISTRY_PASSWORD=$(cat /etc/registry-secret/password)
                REGISTRY_SERVER=$(cat /etc/registry-secret/server 2>/dev/null || echo "")
                
                if [ -n "$REGISTRY_USERNAME" ] && [ -n "$REGISTRY_PASSWORD" ]; then
                    log_info "Found registry credentials, logging in..."
                    if [ -n "$REGISTRY_SERVER" ]; then
                        echo "$REGISTRY_PASSWORD" | run_nerdctl login --username "$REGISTRY_USERNAME" --password-stdin "$REGISTRY_SERVER"
                    else
                        echo "$REGISTRY_PASSWORD" | run_nerdctl login --username "$REGISTRY_USERNAME" --password-stdin
                    fi
                    log_info "Registry login successful"
                else
                    log_warn "Registry credentials found but incomplete"
                fi
            else
                log_warn "Registry secret specified but credentials not found in expected paths"
            fi
        fi
    fi
}

# Push image to registry
push_image() {
    log_info "Pushing image to registry: $IMAGE_NAME"
    
    # Push the image
    if ! run_nerdctl push "$IMAGE_NAME"; then
        log_error "Failed to push image to registry"
        exit 1
    fi
    
    log_info "Image pushed successfully: $IMAGE_NAME"
}

# Get image digest
get_image_digest() {
    log_info "Getting image digest..."
    IMAGE_DIGEST=$(run_nerdctl image inspect "$IMAGE_NAME" --format '{{.RepoDigests}}' 2>/dev/null | head -n1 || echo "")
    if [ -n "$IMAGE_DIGEST" ]; then
        log_info "Image digest: $IMAGE_DIGEST"
        # Write digest to a file that can be read by the controller
        echo "$IMAGE_DIGEST" > /tmp/image-digest
    else
        log_warn "Could not retrieve image digest"
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    # Remove any temporary files if needed
    # For now, we'll keep the committed image as it might be useful
}

# Main execution
main() {
    log_info "Starting notebook image save process..."
    log_info "Container ID: $CONTAINER_ID"
    log_info "Image Name: $IMAGE_NAME"
    log_info "Containerd Socket: $CRI_SOCKET"
    log_info "Nerdctl Namespace: $NERDCTL_NAMESPACE"
    
    # Validate inputs
    validate_env
    
    # Check prerequisites
    check_containerd_socket
    
    # Check if nerdctl is available
    if ! command -v nerdctl &> /dev/null; then
        log_error "nerdctl command not found"
        exit 1
    fi
    
    # Execute the image saving process
    check_container
    setup_registry_auth
    commit_container
    push_image
    get_image_digest
    
    log_info "Image save process completed successfully!"
    log_info "Image: $IMAGE_NAME"
    
    if [ -f /tmp/image-digest ]; then
        log_info "Digest: $(cat /tmp/image-digest)"
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function
main "$@"
