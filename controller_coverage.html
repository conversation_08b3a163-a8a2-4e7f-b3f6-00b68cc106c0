
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>controller: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">cetccloud/cetccloud-operator/internal/controller/notebook_controller.go (62.5%)</option>
				
				<option value="file1">cetccloud/cetccloud-operator/internal/controller/notebook_image_saver_controller.go (23.9%)</option>
				
				<option value="file2">cetccloud/cetccloud-operator/internal/controller/notebook_image_saver_util.go (23.9%)</option>
				
				<option value="file3">cetccloud/cetccloud-operator/internal/controller/notebook_status.go (26.9%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
        "context"

        systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
        api "cetccloud/cetccloud-operator/internal/api"
        "cetccloud/cetccloud-operator/internal/constants"
        "cetccloud/cetccloud-operator/internal/utils"

        // Third-party imports
        "github.com/go-logr/logr"
        appsv1 "k8s.io/api/apps/v1"
        corev1 "k8s.io/api/core/v1"
        k8serrors "k8s.io/apimachinery/pkg/api/errors"
        metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
        "k8s.io/apimachinery/pkg/runtime"
        "k8s.io/apimachinery/pkg/types"
        "k8s.io/client-go/rest"
        "k8s.io/client-go/tools/record"
        ctrl "sigs.k8s.io/controller-runtime"
        "sigs.k8s.io/controller-runtime/pkg/builder"
        "sigs.k8s.io/controller-runtime/pkg/client"
        "sigs.k8s.io/controller-runtime/pkg/handler"
        "sigs.k8s.io/controller-runtime/pkg/log"
        "sigs.k8s.io/controller-runtime/pkg/predicate"
        "sigs.k8s.io/controller-runtime/pkg/reconcile"
)

const DefaultFSGroup = int64(100)

// NotebookReconciler reconciles a Notebook object
type NotebookReconciler struct {
        client.Client
        Log           logr.Logger
        Scheme        *runtime.Scheme
        ClientConfig  *rest.Config
        EventRecorder record.EventRecorder
}

// name of our custom finalizer
const finalizerName = "cetccloud.ai/notebook.finalizers"

// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks,verbs="*"
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks/status,verbs="*"
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks/finalizers,verbs="*"
// +kubebuilder:rbac:groups=core,resources=events,verbs="*"
// +kubebuilder:rbac:groups=core,resources=services,verbs="*"
// +kubebuilder:rbac:groups=core,resources=pods,verbs="*"
// +kubebuilder:rbac:groups=core,resources=serviceaccounts,verbs="*"
// +kubebuilder:rbac:groups=core,resources=secrets,verbs="*"
// +kubebuilder:rbac:groups=apps,resources=statefulsets,verbs="*"
// +kubebuilder:rbac:groups=networking.k8s.io,resources=ingresses,verbs="*"

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the Notebook object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.20.2/pkg/reconcile
func (n *NotebookReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) <span class="cov8" title="1">{
        _ = log.FromContext(ctx)
        log := n.Log.WithValues("notebook", req.NamespacedName)
        log.Info("Reconciliation loop started")

        nb := &amp;systemv1alpha1.Notebook{}

        if err := n.Get(ctx, req.NamespacedName, nb); err != nil </span><span class="cov8" title="1">{
                if k8serrors.IsNotFound(err) </span><span class="cov8" title="1">{
                        return ctrl.Result{}, client.IgnoreNotFound(err)
                }</span>
                <span class="cov0" title="0">return ctrl.Result{}, err</span>
        }

        // 检查对象是否正在被删除
        <span class="cov8" title="1">if !nb.ObjectMeta.DeletionTimestamp.IsZero() </span><span class="cov0" title="0">{
                if utils.Includes(nb.ObjectMeta.Finalizers, finalizerName) </span><span class="cov0" title="0">{
                        n.EventRecorder.Event(nb, corev1.EventTypeNormal, "Terminating", "Terminating notebook")
                        nb.Status.NotebookState = constants.NotebookStateTerminating
                        if err := n.updateAppStatus(ctx, nb); err != nil </span><span class="cov0" title="0">{
                                return n.errorHandler(err)
                        }</span>

                        <span class="cov0" title="0">nb.ObjectMeta.Finalizers = utils.RemoveString(nb.ObjectMeta.Finalizers, finalizerName)
                        if err := n.Update(ctx, nb); err != nil </span><span class="cov0" title="0">{
                                return ctrl.Result{}, err
                        }</span>
                }
                <span class="cov0" title="0">return ctrl.Result{}, nil</span>
        }

        <span class="cov8" title="1">if !utils.Includes(nb.ObjectMeta.Finalizers, finalizerName) </span><span class="cov8" title="1">{
                nb.ObjectMeta.Finalizers = append(nb.ObjectMeta.Finalizers, finalizerName)
                if err := n.Update(ctx, nb); err != nil </span><span class="cov8" title="1">{
                        return ctrl.Result{}, err
                }</span>
        }

        <span class="cov8" title="1">sts := &amp;appsv1.StatefulSet{}
        if err := n.Get(ctx, types.NamespacedName{Namespace: req.Namespace, Name: nb.Name}, sts); err != nil </span><span class="cov8" title="1">{
                n.Log.Info("unable to get sts of Notebook",
                        "notebook", nb.Name,
                        "namespace", req.Namespace,
                        "error", err.Error(),
                )
        }</span>

        <span class="cov8" title="1">foundPod := &amp;corev1.Pod{}
        if err := n.Get(ctx, types.NamespacedName{
                Name:      nb.Name + "-0",
                Namespace: nb.Namespace,
        }, foundPod); err != nil &amp;&amp; k8serrors.IsNotFound(err) </span><span class="cov8" title="1">{
                n.Log.Info("No Pods are currently running for Notebook Server",
                        "notebook", nb.Name,
                        "namespace", nb.Namespace)
        }</span> else<span class="cov0" title="0"> if err != nil </span><span class="cov0" title="0">{
                return ctrl.Result{}, err
        }</span>

        <span class="cov8" title="1">if err := n.updateNotebookStatus(nb, sts, foundPod); err != nil </span><span class="cov8" title="1">{
                n.Log.Error(err, "unable to update notebook status",
                        "notebook", nb.Name,
                        "namespace", nb.Namespace)
                return ctrl.Result{}, err
        }</span>

        <span class="cov8" title="1">done, result, err := n.processNotebookPhase(ctx, nb)
        if done </span><span class="cov0" title="0">{
                return result, err
        }</span>
        <span class="cov8" title="1">return ctrl.Result{}, nil</span>

}

func (n *NotebookReconciler) deleteExternalResources(notebook *systemv1alpha1.Notebook) error <span class="cov0" title="0">{
        n.Log.Info("Deleting external resources", "notebook", notebook.Name)
        // TODO: delete external resource such as pvc ..
        return nil
}</span>

func (n *NotebookReconciler) processNotebookPhase(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) <span class="cov8" title="1">{
        switch notebook.Status.NotebookState </span>{
        case "":<span class="cov8" title="1">
                // Create Notebook
                result, err := n.processCreatState(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return true, result, err
                }</span>
        case constants.NotebookStateCreating:<span class="cov8" title="1">
                done, result, err := n.processCreatingState(ctx, notebook)
                if done </span><span class="cov0" title="0">{
                        return true, result, err
                }</span>
        case constants.NotebookStateRunning:<span class="cov8" title="1">
                done, result, err := n.processRunningState(ctx, notebook)
                if done </span><span class="cov0" title="0">{
                        return true, result, err
                }</span>
        case constants.NotebookStateStopping:<span class="cov8" title="1">
                done, result, err := n.processStoppingState(ctx, notebook)
                if done </span><span class="cov0" title="0">{
                        return true, result, err
                }</span>
        case constants.NotebookStateStopped:<span class="cov8" title="1">
                result, err := n.processStoppedState(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return true, result, err
                }</span>
        }

        <span class="cov8" title="1">return false, ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) processCreatState(ctx context.Context, notebook *systemv1alpha1.Notebook) (ctrl.Result, error) <span class="cov8" title="1">{
        n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Creating", "Creating notebook")
        if err := n.CreateOrUpdateNotebook(ctx, notebook); err != nil </span><span class="cov0" title="0">{
                notebook.Status.NotebookState = constants.NotebookStateFailed
                err := n.updateAppStatus(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return n.errorHandler(err)
                }</span>
        }
        <span class="cov8" title="1">notebook.Status.NotebookState = constants.NotebookStateCreating
        err := n.updateAppStatus(ctx, notebook)
        if err != nil </span><span class="cov0" title="0">{
                return n.errorHandler(err)
        }</span>
        <span class="cov8" title="1">n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Success", constants.CreatingAppMsg)
        return ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) processCreatingState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) <span class="cov8" title="1">{
        if n.shouldStopNotebook(notebook) </span><span class="cov0" title="0">{
                return n.stopNotebook(ctx, notebook)
        }</span>
        <span class="cov8" title="1">if notebook.Status.ReadyReplicas &gt;= 0 </span><span class="cov8" title="1">{
                notebook.Status.NotebookState = constants.NotebookStateRunning
                err := n.updateAppStatus(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return n.handleError(err)
                }</span>
        }
        <span class="cov8" title="1">n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Running", constants.RunningAppMsg)
        return false, ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) processRunningState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) <span class="cov8" title="1">{
        if n.shouldStopNotebook(notebook) </span><span class="cov8" title="1">{
                return n.stopNotebook(ctx, notebook)
        }</span>

        // Check for image updates when notebook is running
        <span class="cov8" title="1">if n.shouldUpdateImage(ctx, notebook) </span><span class="cov0" title="0">{
                _, _, err := n.processUpdate(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return n.handleError(err)
                }</span>
        }

        <span class="cov8" title="1">return false, ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) processStoppingState(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) <span class="cov8" title="1">{
        if notebook.Status.ReadyReplicas &lt;= 0 </span><span class="cov8" title="1">{
                notebook.Status.NotebookState = constants.NotebookStateStopped
                err := n.updateAppStatus(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return n.handleError(err)
                }</span>
        }
        <span class="cov8" title="1">n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Running", string(constants.NotebookStateStopped))
        return false, ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) processStoppedState(ctx context.Context, notebook *systemv1alpha1.Notebook) (ctrl.Result, error) <span class="cov8" title="1">{
        if metav1.HasAnnotation(notebook.ObjectMeta, constants.STOP_ANNOTATION) </span><span class="cov8" title="1">{
                return ctrl.Result{}, nil
        }</span>
        <span class="cov8" title="1">n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Restart", "Restart notebook")
        if err := n.CreateOrUpdateNotebook(ctx, notebook); err != nil </span><span class="cov0" title="0">{
                notebook.Status.NotebookState = constants.NotebookStateFailed
                err := n.updateAppStatus(ctx, notebook)
                if err != nil </span><span class="cov0" title="0">{
                        return n.errorHandler(err)
                }</span>
        }
        <span class="cov8" title="1">notebook.Status.NotebookState = constants.NotebookStateCreating
        err := n.updateAppStatus(ctx, notebook)
        if err != nil </span><span class="cov0" title="0">{
                return n.errorHandler(err)
        }</span>
        <span class="cov8" title="1">n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Success", constants.CreatingAppMsg)
        return ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) shouldStopNotebook(notebook *systemv1alpha1.Notebook) bool <span class="cov8" title="1">{
        return metav1.HasAnnotation(notebook.ObjectMeta, constants.STOP_ANNOTATION)
}</span>

// SetupWithManager sets up the controller with the Manager.
func (n *NotebookReconciler) SetupWithManager(mgr ctrl.Manager) error <span class="cov8" title="1">{
        n.ClientConfig = mgr.GetConfig()
        api.SetupAllBackends(n.Client, n.Scheme)

        if err := mgr.GetFieldIndexer().IndexField(context.Background(), &amp;corev1.Pod{}, constants.PodIndexField, func(object client.Object) []string </span><span class="cov0" title="0">{
                pod := object.(*corev1.Pod)
                if appName, found := pod.Labels[constants.LabelNotebookName]; found </span><span class="cov0" title="0">{
                        return []string{appName}
                }</span>
                <span class="cov0" title="0">return nil</span>
        }); err != nil <span class="cov0" title="0">{
                return err
        }</span>
        <span class="cov8" title="1">return ctrl.NewControllerManagedBy(mgr).
                For(&amp;systemv1alpha1.Notebook{}).
                Owns(&amp;appsv1.StatefulSet{}).
                Owns(&amp;corev1.Service{}).
                Watches(&amp;corev1.Pod{},
                        handler.EnqueueRequestsFromMapFunc(n.findPodsForNoteBook),
                        builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
                ).
                Complete(n)</span>

}

func (n *NotebookReconciler) updateAppStatus(ctx context.Context, notebook *systemv1alpha1.Notebook) error <span class="cov8" title="1">{
        n.Log.Info("updateNoteBookStatus", "app", notebook.Name, "status", notebook.Status.NotebookState)
        if e := n.Status().Update(ctx, notebook); e != nil </span><span class="cov0" title="0">{
                return e
        }</span>
        <span class="cov8" title="1">return nil</span>
}

func (n *NotebookReconciler) CreateOrUpdateNotebook(ctx context.Context, notebook *systemv1alpha1.Notebook) error <span class="cov8" title="1">{
        backends := getAllBackends(notebook)
        for _, backend := range backends </span><span class="cov8" title="1">{
                err := backend.Inject(notebook)
                if err != nil </span><span class="cov0" title="0">{
                        n.Log.Error(err, "Inject failed")
                }</span>
        }

        <span class="cov8" title="1">for _, backend := range backends </span><span class="cov8" title="1">{
                err := backend.Configure(notebook)
                if err != nil </span><span class="cov0" title="0">{
                        n.Log.Error(err, "Configure failed")
                }</span>
        }

        <span class="cov8" title="1">return nil</span>
}

func (r *NotebookReconciler) StopApp(ctx context.Context, notebook *systemv1alpha1.Notebook) error <span class="cov8" title="1">{

        backends := getAllBackends(notebook)
        for _, backend := range backends </span><span class="cov8" title="1">{
                r.Log.Info("Cleaning", "backend", backend.Name(), "is", notebook.Name)
                err := backend.Clean(notebook)
                if err != nil </span><span class="cov0" title="0">{
                        r.Log.Error(err, "Clean failed")
                        // return err
                }</span>
        }
        <span class="cov8" title="1">return nil</span>
}

func getAllBackends(_ *systemv1alpha1.Notebook) []api.BackendInterface <span class="cov8" title="1">{
        return []api.BackendInterface{
                api.GetServiceBackend(),
                api.GetDeployBackend(),
                api.GetGatewayBackend(),
        }

}</span>

func (n *NotebookReconciler) errorHandler(err error) (ctrl.Result, error) <span class="cov0" title="0">{
        if k8serrors.IsConflict(err) </span><span class="cov0" title="0">{
                return reconcile.Result{Requeue: true}, nil
        }</span>
        <span class="cov0" title="0">return ctrl.Result{}, err</span>
}

// shouldUpdateImage checks if the notebook image needs to be updated
func (n *NotebookReconciler) shouldUpdateImage(ctx context.Context, notebook *systemv1alpha1.Notebook) bool <span class="cov8" title="1">{
        // Get the current StatefulSet
        sts := &amp;appsv1.StatefulSet{}
        err := n.Get(ctx, types.NamespacedName{
                Name:      notebook.Name,
                Namespace: notebook.Namespace,
        }, sts)
        if err != nil </span><span class="cov0" title="0">{
                n.Log.Error(err, "Failed to get StatefulSet for image comparison")
                return false
        }</span>

        // Get the desired image from notebook spec
        <span class="cov8" title="1">desiredImage := n.getDesiredImage(notebook)
        if desiredImage == "" </span><span class="cov0" title="0">{
                return false
        }</span>

        // Get the current image from StatefulSet
        <span class="cov8" title="1">currentImage := n.getCurrentImage(sts)
        if currentImage == "" </span><span class="cov0" title="0">{
                return false
        }</span>

        // Compare images
        <span class="cov8" title="1">return desiredImage != currentImage</span>
}

// getDesiredImage extracts the desired image from notebook spec
func (n *NotebookReconciler) getDesiredImage(notebook *systemv1alpha1.Notebook) string <span class="cov8" title="1">{
        if len(notebook.Spec.PodTemplate.Spec.Containers) == 0 </span><span class="cov0" title="0">{
                return ""
        }</span>

        // Find the notebook container
        <span class="cov8" title="1">for _, container := range notebook.Spec.PodTemplate.Spec.Containers </span><span class="cov8" title="1">{
                if container.Name == constants.NotebookContainerName </span><span class="cov8" title="1">{
                        return container.Image
                }</span>
        }

        // If no specific notebook container found, use the first container
        <span class="cov0" title="0">return notebook.Spec.PodTemplate.Spec.Containers[0].Image</span>
}

// getCurrentImage extracts the current image from StatefulSet
func (n *NotebookReconciler) getCurrentImage(sts *appsv1.StatefulSet) string <span class="cov8" title="1">{
        if len(sts.Spec.Template.Spec.Containers) == 0 </span><span class="cov0" title="0">{
                return ""
        }</span>

        // Find the notebook container
        <span class="cov8" title="1">for _, container := range sts.Spec.Template.Spec.Containers </span><span class="cov8" title="1">{
                if container.Name == constants.NotebookContainerName </span><span class="cov8" title="1">{
                        return container.Image
                }</span>
        }

        // If no specific notebook container found, use the first container
        <span class="cov0" title="0">return sts.Spec.Template.Spec.Containers[0].Image</span>
}

// processImageUpdate handles the image update process
func (n *NotebookReconciler) processUpdate(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) <span class="cov0" title="0">{
        n.Log.Info("Processing image update", "notebook", notebook.Name, "namespace", notebook.Namespace)

        // Record event for image update
        n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Updating", "Updating notebook image")

        // Use the standard CreateOrUpdateNotebook method to ensure consistency
        // This will trigger the backend to update the StatefulSet properly
        if err := n.CreateOrUpdateNotebook(ctx, notebook); err != nil </span><span class="cov0" title="0">{
                n.Log.Error(err, "Failed to update notebook resources")
                n.EventRecorder.Event(notebook, corev1.EventTypeWarning, "UpdateFailed", err.Error())
                return n.handleError(err)
        }</span>

        <span class="cov0" title="0">n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Updated", "Notebook image update initiated")
        n.Log.Info("Image update initiated", "notebook", notebook.Name, "namespace", notebook.Namespace)

        return false, ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) stopNotebook(ctx context.Context, notebook *systemv1alpha1.Notebook) (bool, ctrl.Result, error) <span class="cov8" title="1">{
        n.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Stopping", constants.StoppingAppMsg)
        err := n.StopApp(ctx, notebook)
        if err != nil </span><span class="cov0" title="0">{
                return n.handleError(err)
        }</span>

        <span class="cov8" title="1">notebook.Status.NotebookState = constants.NotebookStateStopping
        err = n.updateAppStatus(ctx, notebook)
        if err != nil </span><span class="cov0" title="0">{
                return n.handleError(err)
        }</span>
        <span class="cov8" title="1">return false, ctrl.Result{}, nil</span>
}

func (n *NotebookReconciler) handleError(err error) (bool, ctrl.Result, error) <span class="cov0" title="0">{
        result, err := n.errorHandler(err)
        return true, result, err
}</span>
</pre>
		
		<pre class="file" id="file1" style="display: none">/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
        "context"
        "fmt"
        "time"

        "github.com/go-logr/logr"
        batchv1 "k8s.io/api/batch/v1"
        corev1 "k8s.io/api/core/v1"
        k8serrors "k8s.io/apimachinery/pkg/api/errors"
        "k8s.io/apimachinery/pkg/api/resource"
        metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
        "k8s.io/apimachinery/pkg/runtime"
        "k8s.io/apimachinery/pkg/types"
        "k8s.io/client-go/tools/record"
        ctrl "sigs.k8s.io/controller-runtime"
        "sigs.k8s.io/controller-runtime/pkg/client"
        "sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
        "sigs.k8s.io/controller-runtime/pkg/log"
        "sigs.k8s.io/controller-runtime/pkg/reconcile"

        systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
        "cetccloud/cetccloud-operator/internal/constants"
        "cetccloud/cetccloud-operator/internal/utils"
)

// NotebookImageSaver related constants
const (
        // ImageSaverJobPrefix is the prefix for image saver job names
        ImageSaverJobPrefix  = "isaver"
        CRISocketVolumeName  = "cri-socket"
        RegistryCAVolumeName = "registry-ca"
)

// NotebookImageSaverReconciler reconciles a NotebookImageSaver object
type NotebookImageSaverReconciler struct {
        client.Client
        Logger        logr.Logger
        Scheme        *runtime.Scheme
        EventRecorder record.EventRecorder
}

// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebookimagesavers/finalizers,verbs=update
// +kubebuilder:rbac:groups=cetccloud.ai,resources=notebooks,verbs=get;list;watch
// +kubebuilder:rbac:groups=batch,resources=jobs,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch
// +kubebuilder:rbac:groups="",resources=events,verbs=create;patch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *NotebookImageSaverReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) <span class="cov8" title="1">{
        r.Logger = log.FromContext(ctx, "image-saver", req.Name)
        _ = log.IntoContext(ctx, r.Logger)
        r.Logger.Info("reconciling image-saver")

        // Fetch the NotebookImageSaver instance
        imageSaver := &amp;systemv1alpha1.NotebookImageSaver{}
        if err := r.Get(ctx, req.NamespacedName, imageSaver); err != nil </span><span class="cov8" title="1">{
                if k8serrors.IsNotFound(err) </span><span class="cov8" title="1">{
                        r.Logger.Info("NotebookImageSaver resource not found. Ignoring since object must be deleted")
                        return ctrl.Result{}, nil
                }</span>
                <span class="cov0" title="0">r.Logger.Error(err, "Failed to get NotebookImageSaver")
                return ctrl.Result{}, err</span>
        }

        // Process the image saver based on its current phase
        <span class="cov8" title="1">return r.processImageSaver(ctx, imageSaver)</span>
}

func (r *NotebookImageSaverReconciler) updateStatus(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) <span class="cov8" title="1">{
        r.Logger.Info("updateNoteBookImageSaverStatus", "saver", imageSaver.Name, "status", imageSaver.Status.Phase)
        if e := r.Status().Update(ctx, imageSaver); e != nil &amp;&amp; k8serrors.IsConflict(e) </span><span class="cov0" title="0">{
                return reconcile.Result{Requeue: true}, nil
        }</span>
        <span class="cov8" title="1">return ctrl.Result{}, nil</span>
}

// processImageSaver processes the image saver based on its current phase
func (r *NotebookImageSaverReconciler) processImageSaver(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) <span class="cov8" title="1">{
        switch imageSaver.Status.Phase </span>{
        case "":<span class="cov8" title="1">
                // Initialize the image saver
                return r.initializeImageSaver(ctx, imageSaver)</span>
        case systemv1alpha1.NotebookImageSaverPhasePending:<span class="cov0" title="0">
                // Create the job
                return r.createImageSaverJob(ctx, imageSaver)</span>
        case systemv1alpha1.NotebookImageSaverPhaseRunning:<span class="cov0" title="0">
                // Monitor the job
                return r.monitorImageSaverJob(ctx, imageSaver)</span>
        case systemv1alpha1.NotebookImageSaverPhaseSucceeded, systemv1alpha1.NotebookImageSaverPhaseFailed:<span class="cov8" title="1">
                // Job completed, no further action needed
                r.Logger.Info("Image saver completed", "phase", imageSaver.Status.Phase)
                return ctrl.Result{}, nil</span>
        default:<span class="cov0" title="0">
                r.Logger.Info("Unknown phase", "phase", imageSaver.Status.Phase)
                return ctrl.Result{}, nil</span>
        }
}

// initializeImageSaver initializes the image saver and gathers necessary information
func (r *NotebookImageSaverReconciler) initializeImageSaver(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) <span class="cov8" title="1">{
        notebook := &amp;systemv1alpha1.Notebook{}
        err := r.Get(ctx, types.NamespacedName{
                Name:      imageSaver.Spec.NotebookName,
                Namespace: imageSaver.Namespace,
        }, notebook)
        if err != nil </span><span class="cov8" title="1">{
                if k8serrors.IsNotFound(err) </span><span class="cov8" title="1">{
                        message := fmt.Sprintf("Notebook %s not found", imageSaver.Spec.NotebookName)
                        SetFailedState(imageSaver, "NotebookNotFound", message)
                        return r.updateStatus(ctx, imageSaver)
                }</span>
                <span class="cov0" title="0">r.Logger.Error(err, "Failed to get notebook")
                return ctrl.Result{}, err</span>
        }

        // Check if notebook is running
        <span class="cov8" title="1">if notebook.Status.NotebookState != constants.NotebookStateRunning </span><span class="cov8" title="1">{
                SetFailedState(imageSaver, "NotebookNotRunning", "Notebook is not in running state")
                return r.updateStatus(ctx, imageSaver)
        }</span>

        // set owner reference
        <span class="cov0" title="0">if err := controllerutil.SetControllerReference(notebook, imageSaver, r.Scheme); err != nil </span><span class="cov0" title="0">{
                r.Logger.Error(err, "Failed to set controller reference for ImageSaver")
                return ctrl.Result{}, err
        }</span>
        <span class="cov0" title="0">if err := r.Update(ctx, imageSaver); err != nil </span><span class="cov0" title="0">{
                r.Logger.Error(err, "Failed to update ImageSaver")
                return ctrl.Result{}, err
        }</span>

        // Get the pod
        <span class="cov0" title="0">pod := &amp;corev1.Pod{}
        err = r.Get(ctx, types.NamespacedName{
                Name:      notebook.Name + "-0", // StatefulSet pod naming convention
                Namespace: imageSaver.Namespace,
        }, pod)
        if err != nil </span><span class="cov0" title="0">{
                r.Logger.Error(err, "Failed to get notebook pod")
                SetFailedState(imageSaver, "PodNotFound", "Failed to get notebook pod")
                return r.updateStatus(ctx, imageSaver)
        }</span>

        // Get container name
        <span class="cov0" title="0">containerName := constants.NotebookContainerName

        // Find the container and get its ID
        var containerID string
        for _, containerStatus := range pod.Status.ContainerStatuses </span><span class="cov0" title="0">{
                if containerStatus.Name == containerName </span><span class="cov0" title="0">{
                        containerID = containerStatus.ContainerID
                        break</span>
                }
        }

        <span class="cov0" title="0">if containerID == "" </span><span class="cov0" title="0">{
                message := fmt.Sprintf("Container %s not found in pod", containerName)
                SetFailedState(imageSaver, "ContainerNotFound", message)
                return r.updateStatus(ctx, imageSaver)
        }</span>

        // Update status with gathered information
        <span class="cov0" title="0">SetContainerInfo(imageSaver, pod.Spec.NodeName, containerID)
        SetInitializingState(imageSaver, "Initializing image saver")

        r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "Initialized", "Image saver initialized")
        return r.updateStatus(ctx, imageSaver)</span>
}

// createImageSaverJob creates a job to save the notebook image
func (r *NotebookImageSaverReconciler) createImageSaverJob(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) <span class="cov0" title="0">{
        jobName := fmt.Sprintf("%s-%s", ImageSaverJobPrefix, imageSaver.Name)

        // Check if job already exists
        existingJob := &amp;batchv1.Job{}
        err := r.Get(ctx, types.NamespacedName{Name: jobName, Namespace: imageSaver.Namespace}, existingJob)
        if err == nil </span><span class="cov0" title="0">{
                // Job already exists, update status and move to running phase
                SetJobExistsState(imageSaver, jobName)
                return r.updateStatus(ctx, imageSaver)
        }</span> else<span class="cov0" title="0"> if !k8serrors.IsNotFound(err) </span><span class="cov0" title="0">{
                r.Logger.Error(err, "Failed to get existing job")
                return ctrl.Result{}, err
        }</span>

        // Create the job
        <span class="cov0" title="0">job := r.buildImageSaverJob(imageSaver, jobName)

        // Set owner reference
        if err := controllerutil.SetControllerReference(imageSaver, job, r.Scheme); err != nil </span><span class="cov0" title="0">{
                r.Logger.Error(err, "Failed to set controller reference")
                return ctrl.Result{}, err
        }</span>

        <span class="cov0" title="0">if err := r.Create(ctx, job); err != nil </span><span class="cov0" title="0">{
                r.Logger.Error(err, "Failed to create image saver job")
                SetJobCreationFailedState(imageSaver, err)
                r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "JobCreationFailed", err.Error())
                return r.updateStatus(ctx, imageSaver)
        }</span>

        // Update status
        <span class="cov0" title="0">SetJobCreatedState(imageSaver, jobName)
        r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "JobCreated", "Image saver job created")
        return r.updateStatus(ctx, imageSaver)</span>
}

// buildImageSaverJob builds the job specification for saving the notebook image
func (r *NotebookImageSaverReconciler) buildImageSaverJob(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) *batchv1.Job <span class="cov0" title="0">{
        // Extract container ID without the runtime prefix (e.g., "containerd://")
        containerID := imageSaver.Status.ContainerID

        if len(containerID) &gt; 12 &amp;&amp; containerID[:13] == "containerd://" </span><span class="cov0" title="0">{
                containerID = containerID[13:][:12]
        }</span>

        // Build the full image name
        <span class="cov0" title="0">fullImageName := fmt.Sprintf("%s/%s:%s", utils.GetRegistryAddress(), imageSaver.Spec.Repository, imageSaver.Spec.Tag)

        // Prepare environment variables
        env := []corev1.EnvVar{
                {Name: "CONTAINER_ID", Value: containerID},
                {Name: "IMAGE_NAME", Value: fullImageName},
                {Name: "CRI_SOCKET", Value: utils.GetCRISocket()},
        }

        // Add registry secret if specified
        var imagePullSecrets []corev1.LocalObjectReference
        if imageSaver.Spec.RegistrySecret != "" </span><span class="cov0" title="0">{
                imagePullSecrets = append(imagePullSecrets, corev1.LocalObjectReference{
                        Name: imageSaver.Spec.RegistrySecret,
                })
                env = append(env, corev1.EnvVar{
                        Name:  "REGISTRY_SECRET",
                        Value: imageSaver.Spec.RegistrySecret,
                })
        }</span>

        <span class="cov0" title="0">job := &amp;batchv1.Job{
                ObjectMeta: metav1.ObjectMeta{
                        Name:      jobName,
                        Namespace: imageSaver.Namespace,
                        Labels: map[string]string{
                                "app":                           "notebook-image-saver",
                                "notebook-image-saver.name":     imageSaver.Name,
                                "notebook-image-saver.notebook": imageSaver.Spec.NotebookName,
                        },
                },
                Spec: batchv1.JobSpec{
                        BackoffLimit: &amp;[]int32{0}[0],
                        Template: corev1.PodTemplateSpec{
                                Spec: corev1.PodSpec{
                                        RestartPolicy:    corev1.RestartPolicyNever,
                                        NodeName:         imageSaver.Status.NodeName, // Schedule on the same node as notebook
                                        ImagePullSecrets: imagePullSecrets,
                                        Containers: []corev1.Container{
                                                {
                                                        Name:  "image-saver",
                                                        Image: utils.GetImageSaverImage(),
                                                        Env:   env,
                                                        VolumeMounts: []corev1.VolumeMount{
                                                                {
                                                                        Name:      CRISocketVolumeName,
                                                                        MountPath: utils.GetCRISocket(),
                                                                },
                                                                {
                                                                        Name:      RegistryCAVolumeName,
                                                                        MountPath: "/etc/ssl/certs",
                                                                },
                                                        },
                                                        Resources: corev1.ResourceRequirements{
                                                                Limits: map[corev1.ResourceName]resource.Quantity{
                                                                        corev1.ResourceCPU:    resource.MustParse("2"),
                                                                        corev1.ResourceMemory: resource.MustParse("2Gi"),
                                                                },
                                                                Requests: map[corev1.ResourceName]resource.Quantity{
                                                                        corev1.ResourceCPU:    resource.MustParse("100m"),
                                                                        corev1.ResourceMemory: resource.MustParse("100Mi"),
                                                                },
                                                        },
                                                        SecurityContext: &amp;corev1.SecurityContext{
                                                                Privileged: &amp;[]bool{true}[0], // Need privileged access to containerd socket
                                                        },
                                                },
                                        },
                                        Volumes: []corev1.Volume{
                                                {
                                                        Name: CRISocketVolumeName,
                                                        VolumeSource: corev1.VolumeSource{
                                                                HostPath: &amp;corev1.HostPathVolumeSource{
                                                                        Path: utils.GetCRISocket(),
                                                                        Type: &amp;[]corev1.HostPathType{corev1.HostPathSocket}[0],
                                                                },
                                                        },
                                                },
                                                {
                                                        Name: RegistryCAVolumeName,
                                                        VolumeSource: corev1.VolumeSource{
                                                                Secret: &amp;corev1.SecretVolumeSource{
                                                                        SecretName: RegistryCAVolumeName,
                                                                },
                                                        },
                                                },
                                        },
                                },
                        },
                },
        }

        return job</span>
}

// monitorImageSaverJob monitors the image saver job and updates status accordingly
func (r *NotebookImageSaverReconciler) monitorImageSaverJob(ctx context.Context, imageSaver *systemv1alpha1.NotebookImageSaver) (ctrl.Result, error) <span class="cov0" title="0">{
        // Get the job
        job := &amp;batchv1.Job{}
        err := r.Get(ctx, types.NamespacedName{
                Name:      imageSaver.Status.JobName,
                Namespace: imageSaver.Namespace,
        }, job)
        if err != nil </span><span class="cov0" title="0">{
                if k8serrors.IsNotFound(err) </span><span class="cov0" title="0">{
                        r.Logger.Info("Job not found, marking as failed")
                        SetJobNotFoundState(imageSaver)
                        return r.updateStatus(ctx, imageSaver)
                }</span>
                <span class="cov0" title="0">r.Logger.Error(err, "Failed to get job")
                return ctrl.Result{}, err</span>
        }

        // Check job status
        <span class="cov0" title="0">if completed, conditionType := isBatchJobComplete(job); completed </span><span class="cov0" title="0">{
                switch conditionType </span>{
                case batchv1.JobComplete:<span class="cov0" title="0">
                        // Job succeeded
                        SetImageSavedState(imageSaver)
                        r.EventRecorder.Event(imageSaver, corev1.EventTypeNormal, "ImageSaved", "Notebook image saved successfully")</span>
                case batchv1.JobFailed:<span class="cov0" title="0">
                        // Job failed
                        SetImageSaveFailedState(imageSaver, "Notebook image saving failed")
                        r.EventRecorder.Event(imageSaver, corev1.EventTypeWarning, "ImageSaveFailed", "Notebook image saving failed")</span>
                default:<span class="cov0" title="0">
                        r.Logger.Info("unknown job condition")</span>
                }
                <span class="cov0" title="0">return r.updateStatus(ctx, imageSaver)</span>
        }

        // Job is still running, requeue after some time
        <span class="cov0" title="0">r.Logger.Info("Job is still running, requeuing")
        return ctrl.Result{RequeueAfter: time.Minute}, nil</span>
}

// SetupWithManager sets up the controller with the Manager.
func (r *NotebookImageSaverReconciler) SetupWithManager(mgr ctrl.Manager) error <span class="cov8" title="1">{
        return ctrl.NewControllerManagedBy(mgr).
                For(&amp;systemv1alpha1.NotebookImageSaver{}).
                Owns(&amp;batchv1.Job{}).
                Complete(r)
}</span>
</pre>
		
		<pre class="file" id="file2" style="display: none">/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
        "time"

        batchv1 "k8s.io/api/batch/v1"
        corev1 "k8s.io/api/core/v1"
        metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

        systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
)

// NewCondition creates a new NotebookImageSaver condition.
func NewCondition(condType systemv1alpha1.NotebookImageSaverConditionType, status corev1.ConditionStatus, reason, message string) *systemv1alpha1.NotebookImageSaverCondition <span class="cov8" title="1">{
        return &amp;systemv1alpha1.NotebookImageSaverCondition{
                Type:               condType,
                Status:             status,
                LastProbeTime:      metav1.Now(),
                LastTransitionTime: metav1.Now(),
                Reason:             reason,
                Message:            message,
        }
}</span>

// GetCondition returns the condition with the provided type.
func GetCondition(status systemv1alpha1.NotebookImageSaverStatus, condType systemv1alpha1.NotebookImageSaverConditionType) *systemv1alpha1.NotebookImageSaverCondition <span class="cov8" title="1">{
        for i := range status.Conditions </span><span class="cov0" title="0">{
                c := status.Conditions[i]
                if c.Type == condType </span><span class="cov0" title="0">{
                        return &amp;c
                }</span>
        }
        <span class="cov8" title="1">return nil</span>
}

// SetCondition SetDeploymentCondition updates the deployment to include the provided condition. If the condition that
// we are about to add already exists and has the same status and reason then we are not going to update.
func SetCondition(status *systemv1alpha1.NotebookImageSaverStatus, condition systemv1alpha1.NotebookImageSaverCondition) <span class="cov8" title="1">{
        currentCond := GetCondition(*status, condition.Type)
        if currentCond != nil &amp;&amp; currentCond.Status == condition.Status &amp;&amp; currentCond.Reason == condition.Reason </span><span class="cov0" title="0">{
                return
        }</span>
        // Do not update lastTransitionTime if the status of the condition doesn't change.
        <span class="cov8" title="1">if currentCond != nil &amp;&amp; currentCond.Status == condition.Status </span><span class="cov0" title="0">{
                condition.LastTransitionTime = currentCond.LastTransitionTime
        }</span>
        <span class="cov8" title="1">newConditions := filterOutCondition(status.Conditions, condition.Type)
        status.Conditions = append(newConditions, condition)</span>
}

// RemoveCondition RemoveDeploymentCondition removes the deployment condition with the provided type.
func RemoveCondition(status *systemv1alpha1.NotebookImageSaverStatus, condType systemv1alpha1.NotebookImageSaverConditionType) <span class="cov0" title="0">{
        status.Conditions = filterOutCondition(status.Conditions, condType)
}</span>

// filterOutCondition returns a new slice of deployment conditions without conditions with the provided type.
func filterOutCondition(conditions []systemv1alpha1.NotebookImageSaverCondition, condType systemv1alpha1.NotebookImageSaverConditionType) []systemv1alpha1.NotebookImageSaverCondition <span class="cov8" title="1">{
        var newConditions []systemv1alpha1.NotebookImageSaverCondition
        for _, c := range conditions </span><span class="cov0" title="0">{
                if c.Type == condType </span><span class="cov0" title="0">{
                        continue</span>
                }
                <span class="cov0" title="0">newConditions = append(newConditions, c)</span>
        }
        <span class="cov8" title="1">return newConditions</span>
}

func isBatchJobComplete(job *batchv1.Job) (bool, batchv1.JobConditionType) <span class="cov0" title="0">{
        for _, c := range job.Status.Conditions </span><span class="cov0" title="0">{
                if (c.Type == batchv1.JobComplete || c.Type == batchv1.JobFailed) &amp;&amp; c.Status == corev1.ConditionTrue </span><span class="cov0" title="0">{
                        return true, c.Type
                }</span>
        }

        <span class="cov0" title="0">return false, ""</span>
}

// SetInitializingState sets the image saver to initializing state
func SetInitializingState(imageSaver *systemv1alpha1.NotebookImageSaver, message string) <span class="cov0" title="0">{
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhasePending
        imageSaver.Status.Message = message
        imageSaver.Status.StartTime = &amp;metav1.Time{Time: time.Now()}

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, "Initializing", message)
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetFailedState sets the image saver to failed state
func SetFailedState(imageSaver *systemv1alpha1.NotebookImageSaver, reason, message string) <span class="cov8" title="1">{
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
        imageSaver.Status.Message = message
        imageSaver.Status.CompletionTime = &amp;metav1.Time{Time: time.Now()}

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionFalse, reason, message)
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetJobCreatedState sets the image saver to job created state
func SetJobCreatedState(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) <span class="cov0" title="0">{
        imageSaver.Status.JobName = jobName
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseRunning
        imageSaver.Status.Message = "Image saver job created and running"

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionTrue, "JobCreated", "Image saver job created successfully")
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetJobExistsState sets the image saver to job exists state
func SetJobExistsState(imageSaver *systemv1alpha1.NotebookImageSaver, jobName string) <span class="cov0" title="0">{
        imageSaver.Status.JobName = jobName
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseRunning
        imageSaver.Status.Message = "Image saver job is running"

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionTrue, "JobExists", "Image saver job already exists")
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetJobCreationFailedState sets the image saver to job creation failed state
func SetJobCreationFailedState(imageSaver *systemv1alpha1.NotebookImageSaver, err error) <span class="cov0" title="0">{
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
        imageSaver.Status.Message = "Failed to create image saver job"

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionJobCreated, corev1.ConditionFalse, "JobCreationFailed", err.Error())
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetImageSavedState sets the image saver to succeeded state
func SetImageSavedState(imageSaver *systemv1alpha1.NotebookImageSaver) <span class="cov0" title="0">{
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseSucceeded
        imageSaver.Status.Message = "Image saved successfully"
        imageSaver.Status.CompletionTime = &amp;metav1.Time{Time: time.Now()}

        savedCondition := NewCondition(systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionTrue, "ImageSaved", "Image saved successfully")
        SetCondition(&amp;imageSaver.Status, *savedCondition)

        readyCondition := NewCondition(systemv1alpha1.NotebookImageSaverConditionReady, corev1.ConditionTrue, "ImageSaved", "Notebook image saved successfully")
        SetCondition(&amp;imageSaver.Status, *readyCondition)
}</span>

// SetImageSaveFailedState sets the image saver to failed state due to image save failure
func SetImageSaveFailedState(imageSaver *systemv1alpha1.NotebookImageSaver, reason string) <span class="cov0" title="0">{
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
        imageSaver.Status.Message = reason
        imageSaver.Status.CompletionTime = &amp;metav1.Time{Time: time.Now()}

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionFalse, "ImageSaveFailed", reason)
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetJobNotFoundState sets the image saver to failed state due to job not found
func SetJobNotFoundState(imageSaver *systemv1alpha1.NotebookImageSaver) <span class="cov0" title="0">{
        imageSaver.Status.Phase = systemv1alpha1.NotebookImageSaverPhaseFailed
        imageSaver.Status.Message = "Image saver job not found"
        imageSaver.Status.CompletionTime = &amp;metav1.Time{Time: time.Now()}

        condition := NewCondition(systemv1alpha1.NotebookImageSaverConditionImageSaved, corev1.ConditionFalse, "JobNotFound", "Image saver job not found")
        SetCondition(&amp;imageSaver.Status, *condition)
}</span>

// SetContainerInfo sets container information in the status
func SetContainerInfo(imageSaver *systemv1alpha1.NotebookImageSaver, nodeName, containerID string) <span class="cov0" title="0">{
        imageSaver.Status.NodeName = nodeName
        imageSaver.Status.ContainerID = containerID
}</span>
</pre>
		
		<pre class="file" id="file3" style="display: none">package controller

import (
        systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
        "cetccloud/cetccloud-operator/internal/constants"
        "context"
        "fmt"
        "reflect"
        "time"

        // Third-party imports
        appsv1 "k8s.io/api/apps/v1"
        corev1 "k8s.io/api/core/v1"
        metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
        "k8s.io/apimachinery/pkg/types"
        "sigs.k8s.io/controller-runtime/pkg/client"
        "sigs.k8s.io/controller-runtime/pkg/reconcile"
)

func (n *NotebookReconciler) findPodsForNoteBook(ctx context.Context, object client.Object) []reconcile.Request <span class="cov0" title="0">{
        pod := object.(*corev1.Pod)
        var requests []reconcile.Request
        if pod.Labels[constants.LabelNotebookName] != "" </span><span class="cov0" title="0">{
                requests = []reconcile.Request{
                        {
                                NamespacedName: types.NamespacedName{
                                        Namespace: pod.Namespace,
                                        Name:      pod.Labels[constants.LabelNotebookName],
                                },
                        },
                }
        }</span>
        <span class="cov0" title="0">return requests</span>
}

func (n *NotebookReconciler) updateNotebookStatus(notebook *systemv1alpha1.Notebook,
        sts *appsv1.StatefulSet, pod *corev1.Pod) error <span class="cov8" title="1">{
        log := n.Log.WithValues(
                "namespace", notebook.Namespace,
                "name", notebook.Name,
        )
        ctx := context.Background()

        status, err := n.createNotebookStatus(notebook, sts, pod)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov8" title="1">log.Info("Updating Notebook CR Status", "status", fmt.Sprintf("%+v", status))
        notebook.Status = status
        return n.Status().Update(ctx, notebook)</span>
}

func (n *NotebookReconciler) createNotebookStatus(notebook *systemv1alpha1.Notebook,
        sts *appsv1.StatefulSet, pod *corev1.Pod) (systemv1alpha1.NotebookStatus, error) <span class="cov8" title="1">{

        log := n.Log.WithValues(
                "namespace", notebook.Namespace,
                "name", notebook.Name,
        )
        log.Info("Initializing Notebook CR Status")

        var readyReplicas int32 = 0
        var conditions []systemv1alpha1.NotebookCondition

        if sts == nil </span><span class="cov0" title="0">{
                conditions = []systemv1alpha1.NotebookCondition{
                        {
                                Type:               "Ready",
                                Status:             "False",
                                LastTransitionTime: metav1.Now(),
                                LastProbeTime:      metav1.Now(),
                                Reason:             "Creating",
                                Message:            "StatefulSet is not created yet",
                        },
                }
        }</span> else<span class="cov8" title="1"> {
                readyReplicas = sts.Status.ReadyReplicas
                conditions = make([]systemv1alpha1.NotebookCondition, 0)
        }</span>

        <span class="cov8" title="1">status := systemv1alpha1.NotebookStatus{
                Conditions:     conditions,
                ReadyReplicas:  readyReplicas,
                ContainerState: corev1.ContainerState{},
                NotebookState:  notebook.Status.NotebookState,
        }

        // Update the status based on the Pod's status
        if reflect.DeepEqual(pod.Status, corev1.PodStatus{}) </span><span class="cov8" title="1">{
                log.Info("No pod.Status found. Won't update notebook conditions and containerState")
                return status, nil
        }</span>

        // Update status of the CR using the ContainerState of
        // the container that has the same name as the CR
        <span class="cov0" title="0">notebookContainerFound := false
        log.Info("Calculating Notebook's containerState")
        for i := range pod.Status.ContainerStatuses </span><span class="cov0" title="0">{
                containerName := pod.Status.ContainerStatuses[i].Name
                log.Info("Checking container", "containerName", containerName)

                if containerName == "notebook" || containerName == notebook.Name </span><span class="cov0" title="0">{
                        cs := pod.Status.ContainerStatuses[i].State
                        log.Info("Found notebook container, updating state", "containerState", cs)

                        status.ContainerState = cs
                        notebookContainerFound = true
                        break</span>
                }
        }

        <span class="cov0" title="0">if !notebookContainerFound </span><span class="cov0" title="0">{
                log.Info("Could not find notebook container in Pod's containerStates",
                        "availableContainers", getContainerNames(pod.Status.ContainerStatuses),
                        "expectedName", "notebook")
        }</span>

        // Mirroring pod conditions
        <span class="cov0" title="0">notebookConditions := []systemv1alpha1.NotebookCondition{}
        log.Info("Calculating Notebook's Conditions")
        for i := range pod.Status.Conditions </span><span class="cov0" title="0">{
                condition := n.PodCondToNotebookCond(pod.Status.Conditions[i])
                notebookConditions = append(notebookConditions, condition)
        }</span>

        <span class="cov0" title="0">status.Conditions = notebookConditions

        return status, nil</span>
}

// 辅助函数，用于获取所有容器名称
func getContainerNames(containerStatuses []corev1.ContainerStatus) []string <span class="cov0" title="0">{
        names := make([]string, len(containerStatuses))
        for i, status := range containerStatuses </span><span class="cov0" title="0">{
                names[i] = status.Name
        }</span>
        <span class="cov0" title="0">return names</span>
}

func (n *NotebookReconciler) PodCondToNotebookCond(podc corev1.PodCondition) systemv1alpha1.NotebookCondition <span class="cov0" title="0">{
        condition := systemv1alpha1.NotebookCondition{}

        if len(podc.Type) &gt; 0 </span><span class="cov0" title="0">{
                condition.Type = string(podc.Type)
        }</span>

        <span class="cov0" title="0">if len(podc.Status) &gt; 0 </span><span class="cov0" title="0">{
                condition.Status = string(podc.Status)
        }</span>

        <span class="cov0" title="0">if len(podc.Message) &gt; 0 </span><span class="cov0" title="0">{
                condition.Message = podc.Message
        }</span>

        <span class="cov0" title="0">if len(podc.Reason) &gt; 0 </span><span class="cov0" title="0">{
                condition.Reason = podc.Reason
        }</span>

        // check if podc.LastProbeTime is null. If so initialize
        // the field with metav1.Now()
        <span class="cov0" title="0">check := podc.LastProbeTime.Time.Equal(time.Time{})
        if !check </span><span class="cov0" title="0">{
                condition.LastProbeTime = podc.LastProbeTime
        }</span> else<span class="cov0" title="0"> {
                condition.LastProbeTime = metav1.Now()
        }</span>

        // check if podc.LastTransitionTime is null. If so initialize
        // the field with metav1.Now()
        <span class="cov0" title="0">check = podc.LastTransitionTime.Time.Equal(time.Time{})
        if !check </span><span class="cov0" title="0">{
                condition.LastTransitionTime = podc.LastTransitionTime
        }</span> else<span class="cov0" title="0"> {
                condition.LastTransitionTime = metav1.Now()
        }</span>

        <span class="cov0" title="0">return condition</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
