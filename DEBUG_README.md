# Kubernetes Operator Webhook 本地调试指南

本文档提供了在本地调试 Kubernetes operators 中 webhook 的全面指南，使开发人员能够高效地测试和开发 webhook 功能，而无需重复构建和部署容器镜像。

## 目录

1. [环境准备](#1-环境准备)
   - [创建 Kubernetes 集群](#创建-kubernetes-集群)
   - [本地连接集群](#本地连接集群)
2. [Controller 调试](#2-controller-调试)
   - [生成 Manifests](#生成-manifests)
   - [部署 CRD 到集群](#部署-crd-到集群)
   - [本地启动 Controller](#本地启动-controller)
3. [Webhook 调试](#3-webhook-调试)
   - [自定义 Endpoints 方式](#自定义-endpoints-方式)
   - [配置 Webhook Service](#配置-webhook-service)
   - [配置 CertManager 签发证书](#配置-certmanager-签发证书)
   - [将资源部署到集群](#将资源部署到集群)
   - [复制证书到本地环境](#复制证书到本地环境)
   - [本地启动 Controller](#本地启动-controller-1)
4. [测试](#4-测试)
   - [Webhook 测试](#webhook-测试)
   - [Controller 测试](#controller-测试)
5. [生产部署](#5-生产部署)
   - [构建镜像](#构建镜像)
   - [生成部署 YAML](#生成部署-yaml)
6. [总结](#6-总结)

## 1. 环境准备

### 创建 Kubernetes 集群

要调试 operator，首先需要一个 Kubernetes 集群。您可以使用任何方法创建集群，例如：
- KubeClipper
- Minikube
- Kind
- K3s
- 基于云的 Kubernetes 服务

### 本地连接集群

集群准备好后：

1. 将 kubeconfig 文件复制到本地机器并保存到 `~/.kube/config`
2. 在本地安装 kubectl
3. 使用以下命令验证连接：

```bash
kubectl get po -A
```

对于没有 webhook 的 operator，这个设置足以进行本地调试。带有 webhook 的 operator 需要额外的配置。

## 2. Controller 调试

### 生成 Manifests

为您的 CRD 生成必要的清单文件：

```bash
make manifests
```

此命令将根据您的 operator 代码生成 CRD 和其他部署相关资源的 YAML 文件。

### 部署 CRD 到集群

将 CRD 安装到集群：

```bash
make install
```

此命令使用 kubectl 将 CRD 定义应用到集群。

### 本地启动 Controller

在本地运行 controller：

```bash
make run
```

此命令在本地机器上启动 controller，使用您的 kubeconfig 连接到远程 Kubernetes 集群。您也可以在调试模式下运行它，设置断点并逐步执行代码。

## 3. Webhook 调试

调试 webhook 更复杂，因为 Kubernetes API 服务器需要调用您的 webhook 服务。以下方法使用自定义 Endpoints 来简化流程。

### 自定义 Endpoints 方式

由于 Kubernetes API 服务器通过 Service 访问 webhook，我们将创建一个 Service 并手动定义一个指向本地 IP 地址的 Endpoints 对象。这样，API 服务器的请求将被转发到本地 webhook 服务器。

### 配置 Webhook Service

编辑 webhook 服务配置（`config/webhook/service.yaml`）：

```yaml
apiVersion: v1
kind: Service
metadata:
  name: webhook-service
  namespace: system
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 9443
#  selector:
#    control-plane: controller-manager
---
apiVersion: v1
kind: Endpoints
metadata:
  name: webhook-service
  namespace: system
subsets:
  - addresses:
      - ip: <YOUR_LOCAL_IP>  # 替换为您本地机器的 IP
    ports:
      - port: 9443
        protocol: TCP
```

关键点：
1. 注释掉 Service 中的 `selector` 字段，以防止自动生成 Endpoints
2. 创建与 Service 同名同命名空间的 Endpoints 资源
3. 将 IP 设置为您本地机器的 IP 地址

#### SSH 远程端口转发（替代方案）
如果您的本地机器没有可公开访问的 IP，可以使用 SSH 远程端口转发：

```bash
ssh -N -R 服务器ip:9443:localhost:9443 root@服务器ip
```

这会将从远程服务器的 9443 端口的连接转发到您本地机器的 9443 端口。

为了使其工作，您可能需要在 SSH 服务器配置中启用 `GatewayPorts`：

```bash
# 编辑 SSH 配置
vi /etc/ssh/sshd_config

# 将 GatewayPorts 设置为 yes
GatewayPorts yes

# 重启 SSH 服务
systemctl restart sshd
```

### 配置 CertManager 签发证书

由于 Kubernetes API 服务器使用 HTTPS 访问 webhook，您需要 TLS 证书。推荐使用 cert-manager。

1. 在集群中安装 cert-manager：

```bash
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.16.2/cert-manager.yaml
```

2. 在 kustomization 文件中启用 cert-manager 配置（`config/default/kustomization.yaml`）：

```yaml
# 为所有资源添加命名空间
namespace: your-namespace
namePrefix: your-operator-

resources:
  - ../crd
  - ../rbac
  - ../manager
  - ../webhook
  - ../certmanager  # 取消注释此行
  - metrics_service.yaml

patches:
  - path: manager_metrics_patch.yaml
    target:
      kind: Deployment
  - path: manager_webhook_patch.yaml

# 取消注释 cert-manager 的 replacements 部分
replacements:
  - source:
      kind: Service
      version: v1
      name: webhook-service
      fieldPath: .metadata.name
    targets:
      - select:
          kind: Certificate
          group: cert-manager.io
          version: v1
        fieldPaths:
        # ... (其余的 replacements)
```

### 将资源部署到集群

将所有资源部署到集群：

```bash
make deploy
```

这将部署：
- CRD
- RBAC 资源
- Webhook 的 Service 和 Endpoints
- cert-manager 的 Certificate 和 Issuer 对象

部署后，cert-manager 将自动：
1. 根据 Certificate 和 Issuer 对象生成证书
2. 将其存储在 Secret 中
3. 将 CA bundle 注入到 WebhookConfiguration 中

验证证书是否已创建：

```bash
kubectl get certificate -n your-namespace
kubectl get secret webhook-server-cert -n your-namespace
```

### 复制证书到本地环境

将生成的证书复制到本地机器：

```bash
mkdir -p /tmp/k8s-webhook-server/serving-certs
kubectl get secret -n your-namespace webhook-server-cert -o=jsonpath='{.data.tls\.crt}' | base64 -d > /tmp/k8s-webhook-server/serving-certs/tls.crt
kubectl get secret -n your-namespace webhook-server-cert -o=jsonpath='{.data.tls\.key}' | base64 -d > /tmp/k8s-webhook-server/serving-certs/tls.key
```

Controller 默认会在 `<temp-dir>/k8s-webhook-server/serving-certs/` 目录下查找这些文件。`<temp-dir>` 受 `TMPDIR` 环境变量影响。

### 本地启动 Controller

使用正确的 TMPDIR 启动 controller：

```bash
TMPDIR=/tmp make run
```

您应该会看到指示 webhook 服务器正在启动的日志消息：

```
INFO    controller-runtime.webhook      Starting webhook server
INFO    controller-runtime.webhook      Serving webhook server  {"host": "", "port": 9443}
INFO    controller-runtime.certwatcher  Starting certificate watcher
```

## 4. 测试

### Webhook 测试

通过创建应触发验证或变更的资源来测试您的 webhook：

1. 创建一个应被拒绝的无效资源：

```bash
cat <<EOF | kubectl apply -f -
apiVersion: your-group/v1
kind: YourResource
metadata:
  name: invalid-resource
  namespace: default
spec:
  # 这里是无效字段
EOF
```

您应该会看到来自 webhook 的错误消息。

2. 创建一个有效资源：

```bash
cat <<EOF | kubectl apply -f -
apiVersion: your-group/v1
kind: YourResource
metadata:
  name: valid-resource
  namespace: default
spec:
  # 这里是有效字段
EOF
```

这应该被接受并成功创建。

### Controller 测试

测试您的 controller 的协调逻辑：

1. 创建资源并验证 controller 执行预期操作
2. 更新资源并验证 controller 适当响应
3. 删除资源并验证 controller 清理任何依赖资源

## 5. 生产部署

### 构建镜像

为您的 operator 构建容器镜像：

```bash
make docker-buildx
# 或使用特定参数
IMG=your-registry/controller:latest PLATFORMS=linux/arm64,linux/amd64 make docker-buildx
```

### 生成部署 YAML

在生成部署 YAML 之前，恢复 webhook 服务配置：

1. 取消注释 Service 中的 `selector` 字段
2. 注释掉或删除 Endpoints 对象

然后生成安装 YAML：

```bash
IMG=your-registry/controller:latest make build-installer
```

这将创建一个包含部署 operator 所需的所有资源的 `dist/install.yaml` 文件。

## 6. 总结

带有 webhook 的 Kubernetes operators 的本地调试涉及：

1. **设置集群**并配置本地访问
2. **Controller 调试**：
   - 安装 CRD
   - 在本地运行 controller
3. **Webhook 调试**：
   - 配置带有指向本地机器的自定义 Endpoints 的 Service
   - 设置 cert-manager 签发证书
   - 将证书复制到本地环境
   - 使用正确的证书路径在本地运行 controller
4. **测试**webhook 验证/变更和 controller 协调
5. **生产部署**：
   - 构建容器镜像
   - 生成部署清单

这种方法允许您高效地开发和调试复杂的 operators，而无需为每次代码更改重复构建和部署容器镜像。