🎉 CETC Cloud Operator has been successfully installed!

📋 Operator Information:
   - Namespace: {{ .Release.Namespace }}
   - Release Name: {{ .Release.Name }}
   - Chart Version: {{ .Chart.Version }}
   - App Version: {{ .Chart.AppVersion }}

🔧 Services Available:
{{- if .Values.service.enabled }}
   - Service Name: {{ include "cetccloud-operator.fullname" . }}
   - Service Type: {{ .Values.service.type }}
{{- if .Values.service.metrics.enabled }}
   - Metrics Port: {{ .Values.service.metrics.port }}
{{- end }}
{{- if .Values.service.health.enabled }}
   - Health Check Port: {{ .Values.service.health.port }}
{{- end }}
{{- if .Values.webhook.enabled }}
   - Webhook Port: {{ .Values.webhook.service.port }}
{{- end }}
{{- end }}

🌐 Webhook Configuration:
{{- if .Values.webhook.enabled }}
   ✅ Webhook server is ENABLED
   - Container Port: {{ .Values.webhook.port }}
   - Service Port: {{ .Values.webhook.service.port }}
   - Endpoint: /webhook/result

   📡 To access webhook from within cluster:
   http://{{ include "cetccloud-operator.fullname" . }}.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.webhook.service.port }}/webhook/result

   🔗 To test webhook connectivity:
   kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "cetccloud-operator.fullname" . }} {{ .Values.webhook.service.port }}:{{ .Values.webhook.service.port }}
   # Then access: http://localhost:{{ .Values.webhook.service.port }}/webhook/result
{{- else }}
   ❌ Webhook server is DISABLED
{{- end }}

📊 Monitoring:
{{- if .Values.service.metrics.enabled }}
   - Metrics: kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "cetccloud-operator.fullname" . }} {{ .Values.service.metrics.port }}:{{ .Values.service.metrics.port }}
   - Access metrics at: http://localhost:{{ .Values.service.metrics.port }}/metrics
{{- end }}
{{- if .Values.service.health.enabled }}
   - Health Check: kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "cetccloud-operator.fullname" . }} {{ .Values.service.health.port }}:{{ .Values.service.health.port }}
   - Liveness: http://localhost:{{ .Values.service.health.port }}/healthz
   - Readiness: http://localhost:{{ .Values.service.health.port }}/readyz
{{- end }}

🚀 Next Steps:
   1. Create a ModelEvaluation resource to test the operator
   2. Check operator logs: kubectl --namespace {{ .Release.Namespace }} logs -l app.kubernetes.io/name={{ include "cetccloud-operator.name" . }}
   3. Monitor webhook activity in the logs when ModelEvaluation jobs complete

📚 For more information, visit: https://github.com/your-org/cetccloud-operator
