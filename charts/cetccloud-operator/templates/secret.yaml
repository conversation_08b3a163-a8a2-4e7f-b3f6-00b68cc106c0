{{- if .Values.secret.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.secret.name | default "registry-ca" }}
  namespace: "default"
  labels:
    {{- include "cetccloud-operator.labels" . | nindent 4 }}
  {{- with .Values.secret.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .Values.secret.registryCACert }}
  ca.cert: {{ .Values.secret.registryCACert | b64enc | quote }}
  {{- end }}
{{- end }}
