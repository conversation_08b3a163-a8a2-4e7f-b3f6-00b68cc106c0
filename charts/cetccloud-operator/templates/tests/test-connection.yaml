apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "cetccloud-operator.fullname" . }}-test-connection"
  labels:
    {{- include "cetccloud-operator.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "cetccloud-operator.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
