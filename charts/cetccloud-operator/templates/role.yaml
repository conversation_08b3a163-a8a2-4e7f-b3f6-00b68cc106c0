---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "cetccloud-operator.fullname" . }}-clusterrole
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - '*'
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - '*'
- apiGroups:
  - ""
  resources:
  - secrets
  - serviceaccounts
  - services
  verbs:
  - '*'
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - '*'
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - batch
  resources:
  - jobs/status
  verbs:
  - get
- apiGroups:
  - cetccloud.ai
  resources:
  - modelevaluations
  - notebookimagesavers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - cetccloud.ai
  resources:
  - modelevaluations/finalizers
  - notebookimagesavers/finalizers
  verbs:
  - update
- apiGroups:
  - cetccloud.ai
  resources:
  - modelevaluations/status
  - notebookimagesavers/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - cetccloud.ai
  resources:
  - notebooks
  verbs:
  - '*'
- apiGroups:
  - cetccloud.ai
  resources:
  - notebooks/finalizers
  - notebooks/status
  verbs:
  - '*'
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - '*'
