package app

import (
	"cetccloud/cetccloud-operator/cmd/controller-manager/app/options"
	"cetccloud/cetccloud-operator/internal/controller"

	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog/v2"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/manager"
)

// const (
// 	// 环境变量前缀
// 	webhookEnvPrefix = "ENABLE_WEBHOOK_"
// )

var (
	allControllers = []string{
		"notebook",
		// 在这里添加其他 controller
	}

	// // 定义需要 webhook 的 controllers
	// webhookEnabledControllers = map[string]func(ctrl.Manager) error{
	// 	"notebook": v1alpha1.SetupNotebookWebhookWithManager,
	// 	// 在这里添加其他需要 webhook 的 controller
	// 	// "other-controller": otherpackage.SetupOtherWebhookWithManager,
	// }

	addSuccessfullyControllers = sets.New[string]()
	// addSuccessfullyWebhooks    = sets.New[string]()
)

type setupableController interface {
	SetupWithManager(mgr ctrl.Manager) error
}

// isWebhookEnabled 检查指定controller的webhook是否启用
// 默认为true，只有明确设置为"false"时才禁用
// func isWebhookEnabled(controllerName string) bool {
// 	envName := webhookEnvPrefix + strings.ToUpper(controllerName)
// 	return os.Getenv(envName) != "false"
// }

// addAllControllers setup all available controllers one by one
func addAllControllers(mgr manager.Manager, cmOptions *options.ControllerManagerOptions) error {
	// Setup controllers
	if cmOptions.IsControllerEnabled("notebook") {
		reconciler := &controller.NotebookReconciler{
			Client:        mgr.GetClient(),
			Scheme:        mgr.GetScheme(),
			Log:           ctrl.Log.WithName("controllers").WithName("Notebook"),
			EventRecorder: mgr.GetEventRecorderFor("notebook-controller"),
		}
		addControllerWithSetup(mgr, "notebook", reconciler)
	}

	// Setup webhooks if certificate path is configured
	// if cmOptions.WebhookOptions.CertPath != "" {
	// 	for controllerName, setupFn := range webhookEnabledControllers {
	// 		if cmOptions.IsControllerEnabled(controllerName) && isWebhookEnabled(controllerName) {
	// 			if err := setupFn(mgr); err != nil {
	// 				klog.Errorf("Unable to setup %s webhook: %v", controllerName, err)
	// 				return err
	// 			}
	// 			addSuccessfullyWebhooks.Insert(controllerName)
	// 			klog.Infof("%s webhook has been setup successfully", controllerName)
	// 		} else {
	// 			klog.Infof("%s webhook is explicitly disabled by environment variable", controllerName)
	// 		}
	// 	}
	// } else {
	// 	klog.Info("Webhooks are disabled as no certificate path is configured")
	// }

	// Log controllers status
	for _, name := range allControllers {
		if cmOptions.IsControllerEnabled(name) {
			if addSuccessfullyControllers.Has(name) {
				klog.Infof("%s controller is enabled and added successfully", name)
				// if webhookEnabledControllers[name] != nil {
				// 	if addSuccessfullyWebhooks.Has(name) {
				// 		klog.Infof("%s webhook is enabled and added successfully", name)
				// 	} else if isWebhookEnabled(name) {
				// 		klog.Infof("%s webhook is enabled but failed to configure", name)
				// 	} else {
				// 		klog.Infof("%s webhook is explicitly disabled by environment variable", name)
				// 	}
				// }
			} else {
				klog.Infof("%s controller is enabled but is not going to run due to its dependent component being disabled", name)
			}
		} else {
			klog.Infof("%s controller is disabled by controller selectors", name)
		}
	}

	return nil
}

func addControllerWithSetup(mgr manager.Manager, name string, controller setupableController) {
	if err := controller.SetupWithManager(mgr); err != nil {
		klog.Fatalf("Unable to create %v controller: %v", name, err)
	}
	addSuccessfullyControllers.Insert(name)
}
