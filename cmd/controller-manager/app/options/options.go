package options

import (
	// Standard library imports
	"flag"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	// Third-party imports
	"github.com/spf13/pflag"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/validation/field"
	cliflag "k8s.io/component-base/cli/flag"
	"k8s.io/component-base/config"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
)

// CertificateOptions holds the certificate configuration.
type CertificateOptions struct {
	// CertPath is the directory that contains the certificate.
	CertPath string
	// CertName is the name of the certificate file.
	CertName string
	// KeyName is the name of the key file.
	KeyName string
}

// WebhookOptions holds the webhook configuration.
type WebhookOptions struct {
	CertificateOptions
}

// MetricsOptions holds the metrics configuration.
type MetricsOptions struct {
	// BindAddress is the TCP address that the controller should bind to
	// for serving prometheus metrics. Use :8443 for HTTPS or :8080 for HTTP,
	// or set to "0" to disable the metrics service.
	BindAddress string
	// SecureServing enables serving metrics with authentication and authorization.
	SecureServing bool
	// EnableHTTP2 enables HTTP/2 for the metrics server.
	EnableHTTP2 bool
	// Certificate configuration for metrics server
	CertificateOptions
}

// NewMetricsOptions creates a new MetricsOptions with a default config.
func NewMetricsOptions() *MetricsOptions {
	return &MetricsOptions{
		BindAddress:   "0", // Default to disabled
		SecureServing: false,
		EnableHTTP2:   true,
		CertificateOptions: CertificateOptions{
			CertName: "tls.crt",
			KeyName:  "tls.key",
		},
	}
}

// IsEnabled returns true if metrics server is enabled
func (o *MetricsOptions) IsEnabled() bool {
	return o.BindAddress != "0"
}

// AddFlags adds flags related to metrics for controller manager to the specified FlagSet.
func (o *MetricsOptions) AddFlags(fs *pflag.FlagSet) {
	if fs == nil {
		return
	}

	fs.StringVar(&o.BindAddress, "metrics-bind-address", o.BindAddress,
		"The address the metrics endpoint binds to. Use :8443 for HTTPS or :8080 for HTTP, or set to \"0\" to disable the metrics service.")
	fs.BoolVar(&o.SecureServing, "metrics-secure-serving", o.SecureServing,
		"If true, enable authentication and authorization for the metrics server.")
	fs.BoolVar(&o.EnableHTTP2, "metrics-enable-http2", o.EnableHTTP2,
		"If true, enable HTTP/2 for the metrics server.")
	fs.StringVar(&o.CertPath, "metrics-cert-path", o.CertPath,
		"The directory that contains the metrics server certificate.")
	fs.StringVar(&o.CertName, "metrics-cert-name", o.CertName,
		"The name of the metrics server certificate file.")
	fs.StringVar(&o.KeyName, "metrics-cert-key", o.KeyName,
		"The name of the metrics server key file.")
}

// ControllerManagerOptions holds the configurations for the controller manager.
type ControllerManagerOptions struct {
	// LeaderElect enables leader election.
	LeaderElect bool
	// Namespace is the namespace used to store the ConfigMap for leader election.
	Namespace string
	// LeaderElection defines the configuration of leader election client.
	LeaderElection config.LeaderElectionConfiguration
	// MetricsOptions holds the metrics configuration.
	MetricsOptions *MetricsOptions
	// WebhookOptions holds the webhook configuration.
	WebhookOptions *WebhookOptions
	// HealthProbeBindAddress is the TCP address that the controller should bind to for serving health probes
	// Default is :8081
	HealthProbeBindAddress string
	// ZapOpts holds the zap logger configuration
	ZapOpts zap.Options

	ControllerGates []string
}

// NewControllerManagerOptions creates a new ControllerManagerOptions with a default config.
func NewControllerManagerOptions() *ControllerManagerOptions {
	return &ControllerManagerOptions{
		LeaderElect: false,
		Namespace:   metav1.NamespaceSystem,
		LeaderElection: config.LeaderElectionConfiguration{
			LeaseDuration: metav1.Duration{Duration: 30 * time.Second},
			RenewDeadline: metav1.Duration{Duration: 15 * time.Second},
			RetryPeriod:   metav1.Duration{Duration: 5 * time.Second},
		},
		MetricsOptions: NewMetricsOptions(),
		WebhookOptions: &WebhookOptions{
			CertificateOptions: CertificateOptions{
				CertName: "tls.crt",
				KeyName:  "tls.key",
			},
		},
		HealthProbeBindAddress: ":8081", // Default value matching main.go
		ZapOpts: zap.Options{
			Development: true,
		},
		ControllerGates: []string{"*"},
	}
}

// Flags returns a flagset populated with the controller manager options.
func (o *ControllerManagerOptions) Flags(allControllers []string) cliflag.NamedFlagSets {
	fss := cliflag.NamedFlagSets{}

	fs := fss.FlagSet("leader election")
	fs.BoolVar(&o.LeaderElect, "leader-elect", o.LeaderElect, ""+
		"Start a leader election client and gain leadership before "+
		"executing the main loop. Enable this when running replicated "+
		"components for high availability.")
	fs.StringVar(&o.Namespace, "namespace", o.Namespace, ""+
		"The namespace used to store the ConfigMap for leader election.")
	fs.DurationVar(&o.LeaderElection.LeaseDuration.Duration, "leader-elect-lease-duration", o.LeaderElection.LeaseDuration.Duration, ""+
		"The duration that non-leader candidates will wait after observing a leadership "+
		"renewal until attempting to acquire leadership of a led but unrenewed leader "+
		"slot. This is effectively the maximum duration that a leader can be stopped "+
		"before it is replaced by another candidate. This is only applicable if leader "+
		"election is enabled.")
	fs.DurationVar(&o.LeaderElection.RenewDeadline.Duration, "leader-elect-renew-deadline", o.LeaderElection.RenewDeadline.Duration, ""+
		"The interval between attempts by the acting master to renew a leadership slot "+
		"before it stops leading. This must be less than or equal to the lease duration. "+
		"This is only applicable if leader election is enabled.")
	fs.DurationVar(&o.LeaderElection.RetryPeriod.Duration, "leader-elect-retry-period", o.LeaderElection.RetryPeriod.Duration, ""+
		"The duration the clients should wait between attempting acquisition and renewal "+
		"of a leadership. This is only applicable if leader election is enabled.")

	o.MetricsOptions.AddFlags(fss.FlagSet("metrics"))

	wfs := fss.FlagSet("webhook")
	wfs.StringVar(&o.WebhookOptions.CertPath, "webhook-cert-path", o.WebhookOptions.CertPath,
		"The directory that contains the webhook certificate.")
	wfs.StringVar(&o.WebhookOptions.CertName, "webhook-cert-name", o.WebhookOptions.CertName,
		"The name of the webhook certificate file.")
	wfs.StringVar(&o.WebhookOptions.KeyName, "webhook-cert-key", o.WebhookOptions.KeyName,
		"The name of the webhook key file.")

	gfs := fss.FlagSet("generic")
	gfs.StringVar(&o.HealthProbeBindAddress, "health-probe-bind-address", o.HealthProbeBindAddress,
		"The address the probe endpoint binds to.") // 描述与 main.go 匹配
	gfs.StringSliceVar(&o.ControllerGates, "controllers", []string{"*"}, fmt.Sprintf(""+
		"A list of controllers to enable. '*' enables all on-by-default controllers, 'foo' enables the controller "+
		"named 'foo', '-foo' disables the controller named 'foo'.\nAll controllers: %s",
		strings.Join(allControllers, ", ")))

	// Add klog flags
	kfs := fss.FlagSet("klog")
	local := flag.NewFlagSet("klog", flag.ExitOnError)
	klog.InitFlags(local)
	local.VisitAll(func(fl *flag.Flag) {
		fl.Name = strings.Replace(fl.Name, "_", "-", -1)
		kfs.AddGoFlag(fl)
	})

	return fss
}

// Validate validates all the required options.
func (o *ControllerManagerOptions) Validate(allControllers []string) []error {
	var errs []error

	if o.LeaderElect {
		if o.Namespace == "" {
			errs = append(errs, field.Required(field.NewPath("namespace"), "namespace is required when leader election is enabled"))
		}
		if err := validateLeaderElectionConfiguration(&o.LeaderElection); err != nil {
			errs = append(errs, field.Invalid(field.NewPath("leaderElection"), o.LeaderElection, err.Error()))
		}
	}

	if o.WebhookOptions.CertPath != "" {
		if _, err := filepath.Abs(o.WebhookOptions.CertPath); err != nil {
			errs = append(errs, field.Invalid(field.NewPath("webhookCertPath"), o.WebhookOptions.CertPath, err.Error()))
		}
	}

	return errs
}

// validateLeaderElectionConfiguration validates leader election configuration.
func validateLeaderElectionConfiguration(c *config.LeaderElectionConfiguration) error {
	if c.LeaseDuration.Duration <= 0 {
		return field.Invalid(field.NewPath("leaseDuration"), c.LeaseDuration, "must be greater than 0")
	}
	if c.RenewDeadline.Duration <= 0 {
		return field.Invalid(field.NewPath("renewDeadline"), c.RenewDeadline, "must be greater than 0")
	}
	if c.RetryPeriod.Duration <= 0 {
		return field.Invalid(field.NewPath("retryPeriod"), c.RetryPeriod, "must be greater than 0")
	}
	if c.LeaseDuration.Duration < c.RenewDeadline.Duration {
		return field.Invalid(field.NewPath("leaseDuration"), c.LeaseDuration, "must be greater than renewDeadline")
	}
	return nil
}

func (s *ControllerManagerOptions) IsControllerEnabled(name string) bool {
	hasStar := false
	for _, ctrl := range s.ControllerGates {
		if ctrl == name {
			return true
		}
		if ctrl == "-"+name {
			return false
		}
		if ctrl == "*" {
			hasStar = true
		}
	}
	return hasStar
}
