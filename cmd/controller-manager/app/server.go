package app

import (
	"context"
	"crypto/tls"
	"fmt"
	"os"
	"path/filepath"

	systemv1alpha1 "cetccloud/cetccloud-operator/api/v1alpha1"
	"cetccloud/cetccloud-operator/cmd/controller-manager/app/options"

	// Third-party imports
	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/runtime"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	cliflag "k8s.io/component-base/cli/flag"
	"k8s.io/klog/v2"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/certwatcher"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/metrics/filters"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics/server"
	"sigs.k8s.io/controller-runtime/pkg/webhook"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("controller-manager")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(systemv1alpha1.AddToScheme(scheme))
	// +kubebuilder:scaffold:scheme
}

// NewControllerManagerCommand creates a *cobra.Command object with default parameters
func NewControllerManagerCommand() *cobra.Command {
	s := options.NewControllerManagerOptions()

	cmd := &cobra.Command{
		Use:   "controller-manager",
		Short: "Start the CETC Cloud controller manager",
		Long:  `The CETC Cloud controller manager is a daemon that embeds the core control loops shipped with CETC Cloud.`,
		Run: func(cmd *cobra.Command, args []string) {
			if errs := s.Validate(allControllers); len(errs) != 0 {
				klog.Error(utilerrors.NewAggregate(errs))
				os.Exit(1)
			}

			if err := run(ctrl.SetupSignalHandler(), s); err != nil {
				klog.Error(err)
				os.Exit(1)
			}
		},
		SilenceUsage: true,
	}

	fs := cmd.Flags()
	namedFlagSets := s.Flags(allControllers)
	for _, f := range namedFlagSets.FlagSets {
		fs.AddFlagSet(f)
	}

	usageFmt := "Usage:\n  %s\n"
	cmd.SetHelpFunc(func(cmd *cobra.Command, args []string) {
		_, _ = fmt.Fprintf(cmd.OutOrStdout(), "%s\n\n"+usageFmt, cmd.Long, cmd.UseLine())
		cliflag.PrintSections(cmd.OutOrStdout(), namedFlagSets, 1)
	})

	return cmd
}

func run(ctx context.Context, s *options.ControllerManagerOptions) error {
	// Setup logging first
	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&s.ZapOpts)))

	var tlsOpts []func(*tls.Config)
	if !s.MetricsOptions.EnableHTTP2 {
		tlsOpts = append(tlsOpts, func(c *tls.Config) {
			setupLog.Info("disabling http/2")
			c.NextProtos = []string{"http/1.1"}
		})
	}

	// Initialize certificate watchers
	var metricsCertWatcher, webhookCertWatcher *certwatcher.CertWatcher
	var err error

	// Setup metrics certificate watcher if metrics is enabled and path is provided
	if s.MetricsOptions.IsEnabled() && s.MetricsOptions.CertPath != "" {
		metricsCertWatcher, err = certwatcher.New(
			filepath.Join(s.MetricsOptions.CertPath, s.MetricsOptions.CertName),
			filepath.Join(s.MetricsOptions.CertPath, s.MetricsOptions.KeyName),
		)
		if err != nil {
			return fmt.Errorf("failed to initialize metrics certificate watcher: %v", err)
		}
	}

	// Setup webhook certificate watcher if path is provided
	if s.WebhookOptions.CertPath != "" {
		webhookCertWatcher, err = certwatcher.New(
			filepath.Join(s.WebhookOptions.CertPath, s.WebhookOptions.CertName),
			filepath.Join(s.WebhookOptions.CertPath, s.WebhookOptions.KeyName),
		)
		if err != nil {
			return fmt.Errorf("failed to initialize webhook certificate watcher: %v", err)
		}
	}

	// Configure metrics server options
	metricsServerOptions := metricsserver.Options{
		BindAddress:   s.MetricsOptions.BindAddress,
		SecureServing: s.MetricsOptions.SecureServing,
		TLSOpts:       tlsOpts,
	}

	if !s.MetricsOptions.IsEnabled() {
		setupLog.Info("metrics server is disabled")
	} else {
		if s.MetricsOptions.SecureServing {
			metricsServerOptions.FilterProvider = filters.WithAuthenticationAndAuthorization
		}

		// Add certificate watcher to metrics server if available
		if metricsCertWatcher != nil {
			metricsServerOptions.TLSOpts = append(metricsServerOptions.TLSOpts,
				func(config *tls.Config) {
					config.GetCertificate = metricsCertWatcher.GetCertificate
				})
		}
	}

	// Configure webhook server options
	webhookEnabled := s.WebhookOptions.CertPath != ""
	webhookServerOptions := webhook.Options{
		TLSOpts: tlsOpts,
	}

	if webhookEnabled {
		webhookServerOptions.CertDir = s.WebhookOptions.CertPath

		// Add certificate watcher to webhook server if available
		if webhookCertWatcher != nil {
			webhookServerOptions.TLSOpts = append(webhookServerOptions.TLSOpts,
				func(config *tls.Config) {
					config.GetCertificate = webhookCertWatcher.GetCertificate
				})
		}

		setupLog.Info("Webhook server is enabled")
	} else {
		setupLog.Info("Webhook server is disabled as no certificate path is configured")
	}

	mgrOptions := manager.Options{
		Scheme:                 scheme,
		Metrics:                metricsServerOptions,
		HealthProbeBindAddress: s.HealthProbeBindAddress,
	}

	// 只有在启用 webhook 时才添加 webhook 服务器
	if webhookEnabled {
		mgrOptions.WebhookServer = webhook.NewServer(webhookServerOptions)
		setupLog.Info("Webhook server is enabled with options", "options", webhookServerOptions)
	}

	if s.LeaderElect {
		mgrOptions.LeaderElection = s.LeaderElect
		mgrOptions.LeaderElectionNamespace = s.Namespace
		mgrOptions.LeaderElectionID = "60c77394.cetccloud.ai"
		leaseDuration := s.LeaderElection.LeaseDuration.Duration
		renewDeadline := s.LeaderElection.RenewDeadline.Duration
		retryPeriod := s.LeaderElection.RetryPeriod.Duration
		mgrOptions.LeaseDuration = &leaseDuration
		mgrOptions.RenewDeadline = &renewDeadline
		mgrOptions.RetryPeriod = &retryPeriod
	}

	klog.V(0).Info("setting up manager")

	mgr, err := manager.New(ctrl.GetConfigOrDie(), mgrOptions)
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		return fmt.Errorf("unable to create manager: %v", err)
	}

	// Add certificate watchers to the manager if available
	if metricsCertWatcher != nil {
		if err := mgr.Add(metricsCertWatcher); err != nil {
			return fmt.Errorf("unable to add metrics certificate watcher to manager: %v", err)
		}
	}

	if webhookCertWatcher != nil {
		if err := mgr.Add(webhookCertWatcher); err != nil {
			return fmt.Errorf("unable to add webhook certificate watcher to manager: %v", err)
		}
	}

	// 设置健康检查
	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		return fmt.Errorf("unable to set up health check: %v", err)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		return fmt.Errorf("unable to set up ready check: %v", err)
	}

	// 注册所有控制器
	if err = addAllControllers(mgr, s); err != nil {
		klog.Fatalf("unable to register controllers to the manager: %v", err)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctx); err != nil {
		setupLog.Error(err, "problem running manager")
		return fmt.Errorf("problem running manager: %v", err)
	}

	return nil
}
