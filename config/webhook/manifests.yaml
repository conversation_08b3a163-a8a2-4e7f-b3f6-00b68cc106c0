---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-cetccloud-ai-v1alpha1-notebook
  failurePolicy: Fail
  name: mnotebook.kb.io
  rules:
  - apiGroups:
    - cetccloud.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - notebooks
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-cetccloud-ai-v1alpha1-notebook
  failurePolicy: Fail
  name: vnotebook.kb.io
  rules:
  - apiGroups:
    - cetccloud.ai
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - notebooks
  sideEffects: None
