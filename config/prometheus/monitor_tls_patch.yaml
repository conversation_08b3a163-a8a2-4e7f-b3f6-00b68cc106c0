# Patch for Prometheus ServiceMonitor to enable secure TLS configuration
# using certificates managed by cert-manager
- op: replace
  path: /spec/endpoints/0/tlsConfig
  value:
    # SERVICE_NAME and SERVICE_NAMESPACE will be substituted by kustomize
    serverName: SERVICE_NAME.SERVICE_NAMESPACE.svc
    insecureSkipVerify: false
    ca:
      secret:
        name: metrics-server-cert
        key: ca.crt
    cert:
      secret:
        name: metrics-server-cert
        key: tls.crt
    keySecret:
      name: metrics-server-cert
      key: tls.key
