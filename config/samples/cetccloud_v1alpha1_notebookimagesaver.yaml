apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  labels:
    app.kubernetes.io/name: notebookimagesaver
    app.kubernetes.io/instance: notebookimagesaver-sample
    app.kubernetes.io/part-of: cetccloud-operator
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/created-by: cetccloud-operator
  name: notebookimagesaver-sample
  namespace: default
spec:
  # 要保存镜像的 notebook 名称
  notebookName: "jupyter-sample"
  
  # notebook 所在的命名空间（可选，默认为当前命名空间）
  notebookNamespace: "default"
  
  # 要保存的容器名称（可选，默认为 "notebook"）
  containerName: "notebook"
  
  # 目标镜像仓库
  repository: "my-registry.com/notebooks/jupyter-sample"
  
  # 镜像标签
  tag: "saved-20250103"
  
  # 镜像仓库认证密钥（可选）
  registrySecret: "my-registry-secret"
  
  # 镜像保存器镜像（可选，默认使用 cetccloud/notebook-image-saver:latest）
  imageSaverImage: "cetccloud/notebook-image-saver:latest"
