apiVersion: cetccloud.ai.cetccloud.ai/v1alpha1
kind: ModelEvaluation
metadata:
  labels:
  name: modelevaluation-sample
spec:
  # 评估类型：perf（性能评估）或 eval（功能评估）
  evalType: "eval"
  
  # 要评估的模型名称
  modelName: "Qwen2.5-Omni-7B"
  
  # 模型API端点URL
  apiUrl: "http://10.200.9.249:33817/v1/chat/completions"
  
  # API密钥（可选）
  apiKey: "your-api-key-here"
  
  # 额外的HTTP请求头（可选）
  headers:
    Content-Type: "application/json"
    Authorization: "Bearer token"
  
  # 并行请求数量（默认为1）
  parallel: 2
  
  # 评估样本数量（默认为100）
  number: 5
  
  # 评估任务使用的容器镜像 ,可选 , 默认镜像为 	ModelEvalDefaultImage  = "hub.cetccloud.io:5000/model-evaluation/evalscope:v1.0.0"
  image: "172.28.0.32:3443/ai-platform/model-evaluation/amd64/model-evaluation:latest"

  # Webhook URL，用于接收评估结果（可选）
  # 如果不指定，将使用默认的 webhook URL
  webhookUrl: "http://cetccloud-operator-controller-manager.cetccloud-operator-system.svc.cluster.local:8082/webhook/result"
  
  # 资源请求和限制（可选）
  resources:
    requests:
      cpu: "500m"
      memory: "1Gi"
    limits:
      cpu: "2"
      memory: "4Gi"
  
  # # 模型存储URI列表（可选）
  # modelURIS:
  #   - "s3://model-bucket/model-v1.0"
  #   - "http://model-registry/models/example-model"
  
  # # 数据集URI列表（可选）
  # dataSetURIS:
  #   - "s3://dataset-bucket/eval-dataset.json"
  #   - "http://data-service/datasets/benchmark"
