---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: modelevaluations.cetccloud.ai
spec:
  group: cetccloud.ai
  names:
    kind: ModelEvaluation
    listKind: ModelEvaluationList
    plural: modelevaluations
    shortNames:
    - ej
    singular: modelevaluation
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              api:
                default: openai
                type: string
              apiKey:
                type: string
              apiUrl:
                type: string
              backoffLimit:
                default: 0
                format: int32
                type: integer
              connectTimeout:
                default: 20
                type: integer
              dataSetURIS:
                items:
                  type: string
                type: array
              evalType:
                enum:
                - perf
                - eval
                type: string
              headers:
                additionalProperties:
                  type: string
                type: object
              image:
                type: string
              logEveryNQuery:
                default: 10
                type: integer
              modelName:
                type: string
              modelURIS:
                items:
                  type: string
                type: array
              number:
                default: 100
                type: integer
              parallel:
                default: 1
                type: integer
              readTimeout:
                default: 20
                type: integer
              resources:
                properties:
                  claims:
                    items:
                      properties:
                        name:
                          type: string
                        request:
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  limits:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    type: object
                  requests:
                    additionalProperties:
                      anyOf:
                      - type: integer
                      - type: string
                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                      x-kubernetes-int-or-string: true
                    type: object
                type: object
              temperature:
                default: "0.0"
                type: string
              topK:
                type: string
              topP:
                type: string
              webhookUrl:
                type: string
            required:
            - apiUrl
            - evalType
            - modelName
            type: object
          status:
            properties:
              conditions:
                items:
                  properties:
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              phase:
                type: string
              results:
                properties:
                  downloadUrl:
                    type: string
                  summary:
                    type: string
                  timestamp:
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
