---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: notebookimagesavers.cetccloud.ai
spec:
  group: cetccloud.ai
  names:
    kind: NotebookImageSaver
    listKind: NotebookImageSaverList
    plural: notebookimagesavers
    shortNames:
    - nbis
    singular: notebookimagesaver
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.phase
      name: status
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              containerName:
                type: string
              imageSaverImage:
                type: string
              notebookName:
                type: string
              notebookNamespace:
                type: string
              registrySecret:
                type: string
              repository:
                type: string
              tag:
                type: string
            required:
            - notebookName
            - repository
            - tag
            type: object
          status:
            properties:
              completionTime:
                format: date-time
                type: string
              conditions:
                items:
                  properties:
                    lastProbeTime:
                      format: date-time
                      type: string
                    lastTransitionTime:
                      format: date-time
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              containerID:
                type: string
              imageDigest:
                type: string
              jobName:
                type: string
              message:
                type: string
              nodeName:
                type: string
              phase:
                type: string
              startTime:
                format: date-time
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
