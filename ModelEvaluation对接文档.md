
# ModelEvaluation CRD 后端对接文档

## 📋 文档概述

本文档详细说明 ModelEvaluation 自定义资源的字段定义、使用方法和后端集成要点，用于后端开发人员对接模型评估功能。

## 🔧 API 基本信息

- **API Version**: `cetccloud.ai/v1alpha1`
- **Kind**: `ModelEvaluation`
- **Scope**: Namespaced（命名空间级别）

## 📝 资源结构概览

```yaml
apiVersion: cetccloud.ai/v1alpha1
kind: ModelEvaluation
metadata:
  name: string                    # 资源名称
  namespace: string               # 命名空间（可选）
spec:                            # 规格定义
  # 必填字段
  evalType: string               # 评估类型
  modelName: string              # 模型名称
  apiUrl: string                 # 模型API端点

  # 可选字段
  apiKey: string                 # API密钥
  headers: map[string]string     # HTTP请求头
  parallel: integer              # 并行数
  number: integer                # 评估次数
  webhookUrl: string             # Webhook URL
  image: string                  # 评估镜像
  resources: object              # 资源配置

  # 超时配置
  connectTimeout: integer        # 连接超时（秒）
  readTimeout: integer           # 读取超时（秒）

  # 模型推理参数
  temperature: string            # 温度参数
  topP: string                   # 核采样参数
  topK: string                   # Top-K采样参数
  api: string                    # API接口类型
  logEveryNQuery: integer        # 日志输出频率

status:                          # 状态信息（只读）
  phase: string                  # 当前阶段
  conditions: array              # 详细状态条件
  results: object                # 评估结果
```

## 📋 Spec 字段详细说明

### 必填字段

#### `evalType` - 评估类型
- **类型**: `string`
- **必填**: ✅ 是
- **描述**: 指定评估类型
- **可选值**:
  - `"perf"`: 性能评估（测试模型响应时间、吞吐量等）
  - `"eval"`: 功能评估（测试模型准确性、质量等）
- **示例**: `"perf"`

#### `modelName` - 模型名称
- **类型**: `string`
- **必填**: ✅ 是
- **描述**: 要评估的模型名称，用于标识和记录
- **示例**: `"Qwen-32B"`

#### `apiUrl` - 模型API端点
- **类型**: `string`
- **必填**: ✅ 是
- **描述**: 模型服务的API端点URL
- **格式**: 完整的HTTP/HTTPS URL
- **示例**: `"http://10.200.9.249:16780/openai/v1/chat/completions"`

### 可选字段

#### `apiKey` - API密钥
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 访问模型API所需的认证密钥
- **示例**: `"sk-1234567890abcdef"`

#### `headers` - HTTP请求头
- **类型**: `map[string]string`
- **必填**: ❌ 否
- **描述**: 发送给模型API的额外HTTP请求头
- **示例**:
  ```yaml
  headers:
    Content-Type: "application/json"
    Authorization: "Bearer token"
    X-Custom-Header: "custom-value"
  ```

#### `parallel` - 并行请求数
- **类型**: `integer`
- **必填**: ❌ 否
- **默认值**: `1`
- **范围**: `1-10`
- **描述**: 同时发送的并行请求数量，影响测试负载
- **示例**: `2`

#### `number` - 评估次数
- **类型**: `integer`
- **必填**: ❌ 否
- **默认值**: `100`
- **范围**: `1-1000`
- **描述**: 总的评估请求次数
- **示例**: `5`

#### `webhookUrl` - Webhook回调地址
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 接收评估结果的Webhook URL，如不指定则使用默认地址
- **格式**: 完整的HTTP/HTTPS URL
- **默认值**: 系统自动配置的内部Webhook地址
- **示例**: `"http://127.0.0.1:8082/webhook/result"`

#### `image` - 评估镜像
- **类型**: `string`
- **必填**: ❌ 否
- **默认值**: `"hub.cetccloud.io:5000/model-evaluation/model-evaluation:v1.0.0"`
- **描述**: 执行评估任务的容器镜像
- **示例**: `"hub.cetccloud.io:5000/model-evaluation/model-evaluation:v1.0.0"`

#### `resources` - 资源配置
- **类型**: `object`
- **必填**: ❌ 否
- **描述**: 评估任务的CPU和内存资源配置
- **结构**:
  ```yaml
  resources:
    requests:      # 资源请求（保证分配）
      cpu: string    # CPU请求，如 "500m"
      memory: string # 内存请求，如 "1Gi"
    limits:        # 资源限制（最大使用）
      cpu: string    # CPU限制，如 "2"
      memory: string # 内存限制，如 "4Gi"
  ```
- **示例**:
  ```yaml
  resources:
    requests:
      cpu: "500m"
      memory: "1Gi"
    limits:
      cpu: "2"
      memory: "4Gi"
  ```

#### `connectTimeout` - 连接超时
- **类型**: `integer`
- **必填**: ❌ 否
- **默认值**: `20`
- **范围**: `1-300`
- **描述**: API 请求的连接超时时间（秒）
- **示例**: `30`

#### `readTimeout` - 读取超时
- **类型**: `integer`
- **必填**: ❌ 否
- **默认值**: `20`
- **范围**: `1-600`
- **描述**: API 请求的读取超时时间（秒）
- **示例**: `60`

#### `temperature` - 温度参数
- **类型**: `string`
- **必填**: ❌ 否
- **默认值**: `"0.0"`
- **范围**: `"0.0"-"2.0"`
- **描述**: 控制模型响应的随机性，值越低响应越确定
- **示例**: `"0.7"`

#### `topP` - 核采样参数
- **类型**: `string`
- **必填**: ❌ 否
- **范围**: `"0.0"-"1.0"`
- **描述**: 控制核采样，只考虑累积概率达到此值的令牌
- **示例**: `"0.9"`

#### `topK` - Top-K 采样参数
- **类型**: `string`
- **必填**: ❌ 否
- **范围**: 正整数字符串
- **描述**: 控制 Top-K 采样，只考虑最可能的 K 个令牌
- **示例**: `"50"`

#### `api` - API 接口类型
- **类型**: `string`
- **必填**: ❌ 否
- **默认值**: `"openai"`
- **可选值**: `"openai"`, `"anthropic"`, `"custom"`
- **描述**: 指定使用的 API 接口类型
- **示例**: `"openai"`

#### `logEveryNQuery` - 日志输出频率
- **类型**: `integer`
- **必填**: ❌ 否
- **默认值**: `10`
- **范围**: `1-1000`
- **描述**: 每处理多少个查询输出一次进度日志
- **示例**: `20`

## 📊 Status 字段说明（只读）

### `phase` - 执行阶段
- **类型**: `string`
- **描述**: 当前评估任务的执行阶段
- **可能值**:
  - `"Pending"`: 等待开始
  - `"Running"`: 正在执行
  - `"WaitingForResults"`: 等待结果
  - `"Succeeded"`: 成功完成
  - `"Failed"`: 执行失败
  - `"Terminating"`: 正在终止

### `conditions` - 详细状态条件
- **类型**: `array`
- **描述**: Kubernetes标准的状态条件数组，提供详细的状态信息
- **条件类型**:
  - `JobReady`: Job是否成功创建
  - `JobCompleted`: Job是否完成
  - `ResultsReceived`: 是否收到评估结果
  - `Ready`: 整体就绪状态

### `results` - 评估结果
- **类型**: `object`
- **描述**: 评估完成后的详细结果数据
- **结构**:
  ```yaml
  results:
    summary: string  # JSON格式的结果摘要
  ```
- **示例**:
  ```yaml
  results:
    summary: '{"Average input tokens per request":28.4,"Average latency (s)":36.4892,"Average output tokens per request":1406.8,"Average package latency (s)":0.0259,"Average package per request":1406.8,"Average time per output token (s)":0.0259,"Average time to first token (s)":0.0528,"Failed requests":0,"Number of concurrency":2,"Output token throughput (tok/s)":75.3928,"Request throughput (req/s)":0.0536,"Succeed requests":5,"Time taken for tests (s)":93.298,"Total requests":5,"Total token throughput (tok/s)":76.9148}'
  ```

## 📊 评估结果字段详细说明

当 ModelEvaluation 执行完成后，`status.results.summary` 字段包含 JSON 格式的详细性能指标。以下是各字段的详细说明：

### 🔢 基础统计指标

| 字段名称 | 英文含义 | 中文说明 | 数据类型 | 单位 |
|---------|---------|---------|----------|------|
| `Total requests` | 总请求数 | 测试期间发送的请求总数 | Integer | 个 |
| `Succeed requests` | 成功请求数 | 成功完成并返回预期结果的请求数量 | Integer | 个 |
| `Failed requests` | 失败请求数 | 未能成功完成的请求数量 | Integer | 个 |
| `Number of concurrency` | 并发数 | 同时发送请求的客户端数量 | Integer | 个 |
| `Time taken for tests (s)` | 测试总耗时 | 从第一个请求开始到最后一个请求结束的总时间 | Double | 秒 |

### ⚡ 性能吞吐量指标

| 字段名称 | 英文含义 | 中文说明 | 数据类型 | 单位 | 计算公式 |
|---------|---------|---------|----------|------|---------|
| `Request throughput (req/s)` | 请求吞吐量 | 每秒处理的成功请求数 | Double | 请求/秒 | 成功请求数 ÷ 测试总耗时 |
| `Output token throughput (tok/s)` | 输出令牌吞吐量 | 每秒生成的输出令牌数 | Double | 令牌/秒 | 总输出令牌数 ÷ 测试总耗时 |
| `Total token throughput (tok/s)` | 总令牌吞吐量 | 每秒处理的总令牌数（输入+输出） | Double | 令牌/秒 | 总令牌数 ÷ 测试总耗时 |

### ⏱️ 延迟时间指标

| 字段名称 | 英文含义 | 中文说明 | 数据类型 | 单位 | 重要性 |
|---------|---------|---------|----------|------|--------|
| `Average latency (s)` | 平均延迟 | 从发送请求到接收完整响应的平均时间 | Double | 秒 | ⭐⭐⭐⭐⭐ |
| `Average time to first token (s)` | 平均首令牌时间 | 从发送请求到接收第一个响应令牌的平均时间 | Double | 秒 | ⭐⭐⭐⭐ |
| `Average time per output token (s)` | 平均每输出令牌时间 | 生成每个输出令牌所需的平均时间（不包括首令牌） | Double | 秒 | ⭐⭐⭐⭐ |
| `Average package latency (s)` | 平均包延迟 | 接收每个数据包的平均延迟时间 | Double | 秒 | ⭐⭐⭐ |

### 📝 令牌统计指标

| 字段名称 | 英文含义 | 中文说明 | 数据类型 | 单位 |
|---------|---------|---------|----------|------|
| `Average input tokens per request` | 平均每请求输入令牌数 | 每个请求的平均输入令牌数量 | Double | 个 |
| `Average output tokens per request` | 平均每请求输出令牌数 | 每个请求的平均输出令牌数量 | Double | 个 |
| `Average package per request` | 平均每请求包数 | 每个请求接收的平均数据包数量 | Double | 个 |

### 🎯 关键性能指标解读

#### **响应速度评估**
- **首令牌时间**: 越小越好，表示模型启动响应速度
- **每令牌生成时间**: 越小越好，表示令牌生成效率
- **总体延迟**: 受输出令牌数量影响，需结合令牌数量分析

#### **吞吐量表现**
- **输出吞吐量**: 模型生成令牌的速度，越高越好
- **请求吞吐量**: 系统处理请求的能力，越高越好

#### **稳定性指标**
- **成功率**: (成功请求数 ÷ 总请求数) × 100%
- **并发处理能力**: 在指定并发下的稳定性表现

## 🔄 生命周期状态流转

```
创建资源 → Pending → Running → WaitingForResults → Succeeded
                ↓         ↓            ↓
              Failed    Failed       Failed
```

