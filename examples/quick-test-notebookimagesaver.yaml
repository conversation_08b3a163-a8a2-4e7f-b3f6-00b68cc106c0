# 快速测试用的 NotebookImageSaver 示例
# 使用本地镜像仓库，无需认证

apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: test-image-saver
  namespace: default
  labels:
    test: "true"
    purpose: "quick-test"
spec:
  # 替换为你的 notebook 名称
  notebookName: "jupyter-test"
  
  # 使用本地镜像仓库（假设运行在 localhost:5000）
  repository: "localhost:5000/test/jupyter-notebook"
  
  # 使用时间戳作为标签
  tag: "test-latest"
  
  # 可选：指定容器名称（如果 notebook 有多个容器）
  # containerName: "notebook"
  
  # 可选：使用自定义的镜像保存器镜像
  # imageSaverImage: "cetccloud/notebook-image-saver:latest"

---
# 用于生产环境的示例
apiVersion: cetccloud.ai/v1alpha1
kind: NotebookImageSaver
metadata:
  name: prod-image-saver
  namespace: production
  labels:
    environment: "production"
    team: "data-science"
spec:
  notebookName: "production-notebook"
  repository: "my-registry.com/production/notebook"
  tag: "v1.0.0"
  registrySecret: "production-registry-secret"
