# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/*
Dockerfile.cross

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Go workspace file
go.work

# Kubernetes Generated files - skip generated files, except for vendored files
!vendor/**/zz_generated.*

# editor and IDE paraphernalia
.idea
.vscode
*.swp
*.swo
*~
.cursor/*
.idea/*


.trae/rules/project_rules.md
docker/model-evaluation/perf.yaml
perf.yaml
docker/model-evaluation/__pycache__/*
docker/model-evaluation/outputs/*
docker/model-evaluation/Qwen/Qwen3-32B_benchmark_2025_06_12_14_22_45_924629.db
